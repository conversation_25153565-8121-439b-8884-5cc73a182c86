# tailscale-nat46 快速开始指南

## 🚀 3 分钟快速设置

### 前提条件
- ✅ Windows 10/11 (64位)
- ✅ 管理员权限
- ✅ 已安装并运行 Tailscale

### 步骤 1: 获取 Tailscale IPv6 地址
```cmd
tailscale ip -6
```
记录输出的 IPv6 地址，例如: `fd7a:115c:a1e0:b1a:0:7:a8c0:101`

### 步骤 2: 编辑配置文件
编辑 `config.yaml`:
```yaml
local_ipv4: "************"
local_ipv6: "fd7a:115c:a1e0:b1a:0:7:a8c0:101"  # 替换为您的 IPv6 地址
mappings:
  "********": "fd7a:115c:a1e0:b1a:0:7:c0a8:601"  # 目标设备的 IPv6 地址
interface_name: "tailscale-nat46"
log_level: "info"
```

### 步骤 3: 测试和配置

#### 方法 A: 一键完整测试（推荐）
```cmd
# 以管理员身份运行 - 自动测试所有功能
test-complete.exe
```

#### 方法 B: 分步测试
```cmd
# 3.1 测试 Wintun 接口创建
test-wintun.exe

# 3.2 测试路由管理（需要先创建虚拟接口）
test-routing.exe

# 3.3 测试 IP 配置
test-ip-config.exe
```

#### 方法 C: 手动配置
```cmd
# 配置 IP 地址
configure-ip.bat

# 配置路由规则
configure-routes.bat
```

### 步骤 4: 验证配置

#### 检查接口状态
```cmd
ipconfig /all | findstr -A 5 "tailscale-nat46"
```
应该看到:
```
未知适配器 tailscale-nat46:
   IPv4 地址 . . . . . . . . . . . . : ************
   子网掩码  . . . . . . . . . . . . : *************
```

#### 检查路由
```cmd
route print | findstr "10.1.1"
```
应该看到类似:
```
********    ***************    ************        1
```

#### 测试连通性
```cmd
ping ************  # 测试接口
ping ********      # 测试路由（可能超时，这是正常的）
```

## 🔧 故障排除

### 问题 1: 接口 IP 地址不正确
**现象**: 显示 169.254.x.x 而不是 ************
**解决**: 运行 `configure-ip.bat`

### 问题 2: 路由添加失败
**现象**: 错误代码 87
**解决**: 确保接口 IP 配置正确，然后运行 `configure-routes.bat`

### 问题 3: 权限错误
**现象**: Access denied
**解决**: 以管理员身份运行所有程序

详细故障排除请参考 `TROUBLESHOOTING.md`

## 📁 文件说明

### 主程序
- `tailscale-nat46.exe` - 主程序（开发中）
- `config.yaml` - 配置文件

### 测试工具
- `test-complete.exe` - 完整功能测试（推荐）
- `test-wintun.exe` - 测试 Wintun 接口创建
- `test-routing.exe` - 测试路由管理
- `test-ip-config.exe` - 测试 IP 配置

### 配置脚本
- `configure-ip.bat` - 配置接口 IP 地址
- `configure-routes.bat` - 配置路由规则

### 文档
- `QUICK-START.md` - 本文件
- `TROUBLESHOOTING.md` - 详细故障排除指南

## 🎯 使用场景示例

### 场景: 访问 IPv6-only 的 Linux 服务器

1. **Linux 服务器** (Tailscale IPv6: `fd7a:115c:a1e0:b1a:0:7:c0a8:601`)
2. **Windows 客户端** (运行 tailscale-nat46)
3. **IPv4 应用** (只支持 IPv4，需要访问 Linux 服务器)

**配置**:
```yaml
mappings:
  "********": "fd7a:115c:a1e0:b1a:0:7:c0a8:601"
```

**使用**:
IPv4 应用连接到 `********:80`，实际访问 `[fd7a:115c:a1e0:b1a:0:7:c0a8:601]:80`

## 📞 获取帮助

1. **查看日志**: 程序运行时的详细输出
2. **检查配置**: 确保 `config.yaml` 正确
3. **运行测试**: 使用提供的测试工具诊断问题
4. **参考文档**: `TROUBLESHOOTING.md` 包含详细解决方案

## ⚠️ 重要提示

- **管理员权限**: 所有操作都需要管理员权限
- **防火墙**: 可能需要配置 Windows 防火墙规则
- **Tailscale**: 确保 Tailscale 正常运行
- **备份**: 修改网络配置前建议备份当前设置

---

**项目状态**: 🚧 开发中 - 核心网络组件已完成，数据包转换功能开发中

**版本**: 1.0.0-alpha

**最后更新**: 2024-08-22
