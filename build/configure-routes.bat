@echo off
chcp 65001 >nul
title 配置 tailscale-nat46 路由

echo ========================================
echo tailscale-nat46 路由配置脚本
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限
    echo 请以管理员身份运行此脚本
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

REM 设置默认值
set GATEWAY_IP=************
set TARGET_IPS=******** ******** ********

REM 允许用户自定义网关
if "%1" neq "" set GATEWAY_IP=%1

echo 配置参数:
echo   网关 IP: %GATEWAY_IP%
echo   目标 IPs: %TARGET_IPS%
echo.

REM 检查网关是否可达
echo 1. 检查网关连通性...
ping -n 1 %GATEWAY_IP% >nul 2>&1
if %errorLevel% neq 0 (
    echo ⚠️  警告：网关 %GATEWAY_IP% 不可达
    echo 请确保 tailscale-nat46 接口已正确配置
    echo.
    echo 您可以运行以下命令配置接口:
    echo   configure-ip.bat
    echo.
    choice /C YN /M "是否继续添加路由"
    if errorlevel 2 exit /b 1
) else (
    echo ✅ 网关 %GATEWAY_IP% 可达
)

echo.
echo 2. 添加路由规则...

REM 添加每个目标 IP 的路由
for %%i in (%TARGET_IPS%) do (
    echo.
    echo 添加路由: %%i -> %GATEWAY_IP%
    echo 执行命令: route add %%i mask *************** %GATEWAY_IP% metric 1
    
    route add %%i mask *************** %GATEWAY_IP% metric 1
    if !errorLevel! equ 0 (
        echo ✅ 路由 %%i 添加成功
    ) else (
        echo ❌ 路由 %%i 添加失败
    )
)

echo.
echo 3. 验证路由配置...
echo.
echo 当前路由表（相关条目）:
for %%i in (%TARGET_IPS%) do (
    echo.
    echo 路由到 %%i:
    route print | findstr "%%i"
    if !errorLevel! neq 0 (
        echo   （未找到路由条目）
    )
)

echo.
echo ========================================
echo 路由配置完成
echo ========================================
echo.
echo 配置摘要:
echo   网关: %GATEWAY_IP%
echo   目标 IPs: %TARGET_IPS%
echo.
echo 测试连接:
for %%i in (%TARGET_IPS%) do (
    echo   ping %%i
)
echo.
echo 查看完整路由表:
echo   route print
echo.
echo 删除路由（如需要）:
for %%i in (%TARGET_IPS%) do (
    echo   route delete %%i
)
echo.

REM 提供交互式测试选项
echo 是否要测试连接？
choice /C YN /M "测试 ping 连接"
if errorlevel 2 goto :end

echo.
echo 4. 测试连接...
for %%i in (%TARGET_IPS%) do (
    echo.
    echo 测试连接到 %%i...
    ping -n 2 %%i
    echo.
)

:end
echo.
echo 脚本执行完成！
pause
