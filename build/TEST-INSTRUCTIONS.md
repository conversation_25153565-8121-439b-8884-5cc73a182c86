# tailscale-nat46 测试说明

## 🚀 推荐测试流程

### 1. 完整自动测试（最简单）

```cmd
# 以管理员身份运行命令提示符
# 进入 build 目录
cd build

# 运行完整测试
test-complete.exe
```

**这个程序会自动：**
- ✅ 创建 Wintun 虚拟网卡
- ✅ 配置 IP 地址 (************)
- ✅ 添加测试路由 (********, ********, ********)
- ✅ 验证配置
- ✅ 提供测试建议
- ✅ 自动清理

### 2. 验证结果

测试运行后，您应该看到类似输出：
```
=== Test Results ===
Interface: test-complete
IP Address: ************
Routes Added: 3/3

You can now test:
  1. Ping interface: ping ************
  2. Test route: ping ********
  3. Test route: ping ********
  4. Test route: ping ********
  3. Check routing table: route print
  4. Check interface: ipconfig /all
```

### 3. 手动验证（可选）

在测试程序运行时，打开另一个管理员命令提示符：

```cmd
# 检查接口配置
ipconfig /all | findstr -A 5 "test-complete"

# 检查路由表
route print | findstr "10.1.1"

# 测试连通性
ping ************
ping ********
```

## 🔧 分步测试（故障排除）

如果完整测试失败，可以分步测试：

### 步骤 1: 测试 Wintun 接口
```cmd
test-wintun.exe
```
**预期结果**: 创建虚拟网卡，配置 IP 地址

### 步骤 2: 测试路由管理
```cmd
test-routing.exe
```
**预期结果**: 找到虚拟接口，成功添加路由

### 步骤 3: 测试路由到正确接口
```cmd
test-route-interface.exe
```
**预期结果**: 路由指向虚拟接口而不是物理接口

### 步骤 4: 测试 IP 配置
```cmd
test-ip-config.exe
```
**预期结果**: 验证 IP 配置正确

### 步骤 5: 路由诊断
```cmd
diagnose-routing.bat
```
**预期结果**: 详细的路由诊断信息和修复建议

## 🛠️ 手动配置（备用方案）

如果自动测试失败，可以手动配置：

### 1. 手动配置 IP
```cmd
configure-ip.bat
```

### 2. 手动配置路由
```cmd
configure-routes.bat
```

## ❌ 常见问题解决

### 问题 1: "Access denied" 错误
**解决**: 确保以管理员身份运行

### 问题 2: 接口创建失败
**解决**: 
1. 检查是否安装了 Tailscale（包含 Wintun 驱动）
2. 重启计算机
3. 以管理员身份重试

### 问题 3: IP 地址显示 169.254.x.x
**解决**: 
1. 运行 `configure-ip.bat`
2. 或手动配置：
   ```cmd
   netsh interface ip set address "接口名" static ************ *************
   ```

### 问题 4: 路由添加失败
**解决**:
1. 确保虚拟接口 IP 配置正确
2. 运行 `configure-routes.bat`
3. 或手动添加：
   ```cmd
   route add ******** mask *************** ************ metric 1
   ```

## 📊 测试结果解读

### 成功指标
- ✅ 虚拟接口创建成功
- ✅ IP 地址配置为 ************
- ✅ 路由添加成功（3/3）
- ✅ ping ************ 成功

### 部分成功
- ⚠️ 接口创建成功，但 IP 配置失败
- ⚠️ IP 配置成功，但路由添加失败
- ⚠️ 路由部分添加成功（1/3 或 2/3）

### 失败指标
- ❌ 接口创建失败
- ❌ 权限错误
- ❌ 驱动程序问题

## 🔍 调试信息收集

如果遇到问题，请收集以下信息：

```cmd
# 系统信息
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"

# 网络配置
ipconfig /all > network-config.txt

# 路由表
route print > route-table.txt

# 接口状态
netsh interface show interface > interfaces.txt

# Tailscale 状态（如果安装）
tailscale status > tailscale-status.txt
```

## 📞 获取帮助

1. **查看详细日志**: 测试程序会显示详细的执行日志
2. **参考故障排除**: 查看 `TROUBLESHOOTING.md`
3. **检查配置**: 确保 `config.yaml` 正确
4. **重新测试**: 清理后重新运行测试

## ⚡ 快速命令参考

```cmd
# 完整测试
test-complete.exe

# 检查配置
ipconfig /all | findstr -A 5 "test"
route print | findstr "10.1.1"

# 手动配置
configure-ip.bat
configure-routes.bat

# 清理（如果需要）
route delete ********
route delete ********
route delete ********
netsh interface set interface "接口名" admin=disable
```

---

**重要提示**: 
- 所有操作都需要管理员权限
- 测试会创建临时网络配置，程序退出时会自动清理
- 如果程序异常退出，可能需要手动清理路由和接口

**项目状态**: 🚧 开发中 - 网络基础设施已完成，数据包转换功能开发中
