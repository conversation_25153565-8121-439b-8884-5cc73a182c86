@echo off
echo Debugging tailscale-nat46 connection...
echo.

echo 1. Testing direct IPv6 connection...
echo Trying curl to IPv6 address...
curl -m 5 -v "http://[fd7a:115c:a1e0:b1a:0:7:c0a8:601]/" 2>&1 | findstr /C:"Connected" /C:"HTTP" /C:"curl:"
echo.

echo 2. Testing IPv4 proxy connection...
echo Trying curl to IPv4 address (should be proxied)...
curl -m 5 -v "http://********/" 2>&1 | findstr /C:"Connected" /C:"HTTP" /C:"curl:"
echo.

echo 3. Testing with telnet to port 80...
echo Testing TCP connection to ********:80...
echo exit | telnet ******** 80 2>&1 | findstr /C:"Connected" /C:"Could not"
echo.

echo 4. Checking if port 80 is listening on ********...
netstat -an | findstr "********:80"
echo.

echo 5. Checking Tailscale connectivity...
ping -6 -n 2 fd7a:115c:a1e0:b1a:0:7:c0a8:601 2>nul
if %errorLevel% equ 0 (
    echo ✓ IPv6 ping successful
) else (
    echo ✗ IPv6 ping failed
)
echo.

echo 6. Checking current network connections...
netstat -an | findstr ":80 "
echo.

echo Debug complete!
echo.
echo If IPv6 works but IPv4 doesn't, the issue is in the proxy.
echo If neither works, check Tailscale connectivity.
echo.
pause
