# tailscale-nat46 Configuration Example
# 
# This file demonstrates how to configure the IPv4-to-IPv6 transparent proxy
# for Tailscale networks.

# Local IPv4 address for the virtual interface
# This address will be assigned to the Wintun virtual network interface
local_ipv4: "************"

# Your Tailscale IPv6 address
# Get this with: tailscale ip -6
# This is the source address for outgoing IPv6 packets
local_ipv6: "fd7a:115c:a1e0:b1a:0:7:a8c0:101"

# IP mappings: IPv4 -> IPv6
# Define which IPv4 addresses should be translated to which IPv6 addresses
# The IPv4 addresses are what your applications will connect to
# The IPv6 addresses are the actual Tailscale addresses of your devices
mappings:
  # Example: Map ******** to a Tailscale IPv6 address
  "********": "fd7a:115c:a1e0:b1a:0:7:c0a8:601"
  
  # Example: Map ******** to another Tailscale IPv6 address
  "********": "fd7a:115c:a1e0:b1a:0:7:c0a8:602"
  
  # Add more mappings as needed
  # "********": "fd7a:115c:a1e0:b1a:0:7:c0a8:603"

# Virtual interface name (optional)
# Default: "tailscale-nat46"
interface_name: "tailscale-nat46"

# Logging configuration
log_level: "info"  # debug, info, warn, error
log_file: ""       # Empty string means log to stdout only
                   # Set to a file path to also log to file

# Network configuration (optional)
mtu: 1500          # Maximum Transmission Unit
buffer_size: 65536 # Buffer size for packet processing
workers: 4         # Number of worker goroutines

# Debug mode (optional)
debug: false       # Enable debug mode for verbose logging

# Example configuration for a typical setup:
#
# 1. Your Windows machine has Tailscale running
# 2. You want to access a Linux server at fd7a:115c:a1e0:b1a:0:7:c0a8:601
# 3. Your legacy application only supports IPv4
# 4. You want the application to connect to ******** instead
#
# With this configuration:
# - The tool creates a virtual interface with IP ************
# - It adds a route: ******** -> ************
# - When your app connects to ********, packets go to the virtual interface
# - The tool translates IPv4 packets to IPv6 and sends them to the Linux server
# - Response packets are translated back to IPv4 and sent to your application
