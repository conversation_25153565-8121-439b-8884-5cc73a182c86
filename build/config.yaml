# goproxy Configuration Example
#
# This file demonstrates how to configure the IPv4 to IPv6 forwarding proxy
# for industrial network traffic forwarding.

# Local IPv4 address for the virtual interface
# This address will be assigned to the Wintun virtual network interface
local_ipv4: "************"

# IPv4 to IPv6 mappings
# Define which IPv4 addresses should be forwarded to which IPv6 addresses
# When clients access these IPv4 addresses, traffic will be forwarded to the corresponding IPv6 addresses
mappings:
  # Example: Forward ******** to IPv6 address
  "********": "2001:db8::100"

  # Example: Forward ******** to another IPv6 address
  "********": "2001:db8::200"

  # Example: Forward ******** to yet another IPv6 address
  "********": "2001:db8::300"

  # Add more mappings as needed
  # "********": "2001:db8::400"

# Virtual interface name (optional)
# Default: "goproxy"
interface_name: "goproxy"

# Logging configuration
log_level: "info"  # debug, info, warn, error
log_file: ""       # Empty string means log to stdout only
                   # Set to a file path to also log to file

# Network configuration (optional)
mtu: 1500          # Maximum Transmission Unit
buffer_size: 65536 # Buffer size for packet processing
workers: 4         # Number of worker goroutines

# Debug mode (optional)
debug: false       # Enable debug mode for verbose logging

# Example configuration for a typical setup:
#
# 1. Your application needs to access IPv6 services using IPv4 addresses
# 2. Configure the mappings to point IPv4 addresses to IPv6 addresses
# 3. The virtual interface will capture and forward the traffic
#
# With this configuration:
# - The tool creates a virtual interface with IP ************
# - It captures packets destined for 10.1.1.x addresses
# - When a client connects to ********, traffic is forwarded to 2001:db8::100
# - Response traffic from the IPv6 server is sent back to the client
# - Full TCP connection state is maintained for proper bidirectional communication
