# goproxy Configuration Example
#
# This file demonstrates how to configure the port forwarding proxy
# for industrial network traffic forwarding.

# Local IPv4 address for the virtual interface (target IP)
# This address will be assigned to the Wintun virtual network interface
# All forwarded traffic will be sent to this IP
local_ipv4: "************"

# Port mappings for forwarding
# Define which ports should be forwarded to the virtual interface
port_mappings:
  # Example: Forward TCP port 8080
  - listen_port: 8080
    protocol: "tcp"

  # Example: Forward UDP port 53 (DNS)
  - listen_port: 53
    protocol: "udp"

  # Example: Forward both TCP and UDP on port 80
  - listen_port: 80
    protocol: "both"

  # Add more port mappings as needed
  # - listen_port: 443
  #   protocol: "tcp"

# Virtual interface name (optional)
# Default: "goproxy"
interface_name: "goproxy"

# Logging configuration
log_level: "info"  # debug, info, warn, error
log_file: ""       # Empty string means log to stdout only
                   # Set to a file path to also log to file

# Network configuration (optional)
mtu: 1500          # Maximum Transmission Unit
buffer_size: 65536 # Buffer size for packet processing
workers: 4         # Number of worker goroutines

# Debug mode (optional)
debug: false       # Enable debug mode for verbose logging

# Example configuration for a typical setup:
#
# 1. Your application needs to access a service on a specific port
# 2. You want to forward traffic to the virtual network interface
# 3. The virtual interface will handle the traffic processing
#
# With this configuration:
# - The tool creates a virtual interface with IP ************
# - It listens on the specified ports (8080, 53, 80)
# - When a client connects to these ports, traffic is forwarded to ************:same_port
# - Response traffic from the virtual interface is sent back to the client
# - No protocol translation is performed - pure port forwarding
