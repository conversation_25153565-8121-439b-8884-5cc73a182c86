@echo off
echo Setting up HTTP proxy for tailscale-nat46...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Step 1: Setting up port forwarding for ********:80 -> 127.0.0.1:8080
netsh interface portproxy add v4tov4 listenaddress=******** listenport=80 connectaddress=127.0.0.1 connectport=8080

echo Step 2: Setting up port forwarding for ********:80 -> 127.0.0.1:8080
netsh interface portproxy add v4tov4 listenaddress=******** listenport=80 connectaddress=127.0.0.1 connectport=8080

echo.
echo Step 3: Showing current port proxy configuration
netsh interface portproxy show all

echo.
echo Setup complete!
echo.
echo Now you can:
echo 1. Start tailscale-nat46.exe (it will listen on port 8080)
echo 2. Access http://********/ (will be forwarded to the proxy)
echo.
pause
