@echo off
echo Testing tailscale-nat46 connection...
echo.

echo 1. Testing direct IPv6 connection...
echo Trying to connect to fd7a:115c:a1e0:b1a:0:7:c0a8:601...
curl -m 5 "http://[fd7a:115c:a1e0:b1a:0:7:c0a8:601]/" 2>nul
if %errorLevel% equ 0 (
    echo ✓ Direct IPv6 connection works
) else (
    echo ✗ Direct IPv6 connection failed
)
echo.

echo 2. Testing IPv4 to IPv6 translation...
echo Trying to connect to ******** (should translate to IPv6)...
curl -m 5 "http://********/" 2>nul
if %errorLevel% equ 0 (
    echo ✓ IPv4 to IPv6 translation works!
) else (
    echo ✗ IPv4 to IPv6 translation failed
)
echo.

echo 3. Checking current routes...
route print | findstr "10.1.1"
echo.

echo Test complete!
pause
