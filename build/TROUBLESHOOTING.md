# tailscale-nat46 故障排除指南

## 常见问题和解决方案

### 1. Wintun 虚拟网卡 IP 地址配置问题

#### 问题现象
- 创建的虚拟网卡显示自动配置的 169.254.x.x 地址
- 而不是配置文件中指定的 IP 地址（如 ************）

#### 原因分析
- Windows API 调用可能失败
- 需要管理员权限
- 接口配置时序问题

#### 解决方案

**方案 1: 使用配置脚本（推荐）**
```cmd
# 以管理员身份运行
configure-ip.bat
```

**方案 2: 手动配置**
```cmd
# 1. 查看接口名称
netsh interface show interface

# 2. 配置 IP 地址
netsh interface ip set address "tailscale-nat46" static ************ *************

# 3. 启用接口
netsh interface set interface "tailscale-nat46" admin=enable
```

**方案 3: 使用 Windows 网络设置**
1. 打开"网络和共享中心"
2. 点击"更改适配器设置"
3. 找到 "tailscale-nat46" 接口
4. 右键 -> 属性 -> Internet 协议版本 4 (TCP/IPv4)
5. 设置静态 IP: ************，子网掩码: *************

### 2. 路由添加失败问题

#### 问题现象
- 错误代码 87 (ERROR_INVALID_PARAMETER)
- 路由添加失败

#### 原因分析
- 网关地址不在任何已知网络上
- 接口没有正确的 IP 配置
- 权限不足

#### 解决方案

**步骤 1: 确保接口 IP 配置正确**
```cmd
# 检查接口配置
ipconfig /all | findstr -A 10 "tailscale-nat46"

# 应该看到类似输出:
# 未知适配器 tailscale-nat46:
#    IPv4 地址 . . . . . . . . . . . . : ************
#    子网掩码  . . . . . . . . . . . . : *************
```

**步骤 2: 使用正确的网关地址**
```cmd
# 使用接口自己的 IP 作为网关
route add ******** mask *************** ************ metric 1
```

**步骤 3: 使用配置脚本**
```cmd
# 以管理员身份运行
configure-routes.bat
```

### 3. 权限问题

#### 问题现象
- "Access denied" 错误
- 无法创建虚拟网卡
- 无法修改路由表

#### 解决方案
1. **以管理员身份运行所有程序**
2. **检查防病毒软件设置**
3. **确保 Wintun 驱动已安装**

### 4. 接口创建失败

#### 问题现象
- Wintun 接口创建失败
- "Driver not found" 错误

#### 解决方案

**检查 Wintun 驱动**
```cmd
# 检查是否已安装 Tailscale（包含 Wintun 驱动）
sc query tailscale

# 或检查设备管理器中的网络适配器
```

**重新安装 Tailscale**
如果 Wintun 驱动缺失，重新安装 Tailscale 客户端。

### 5. 网络连通性测试

#### 基本测试步骤

**1. 检查接口状态**
```cmd
netsh interface show interface
```

**2. 检查 IP 配置**
```cmd
ipconfig /all
```

**3. 测试本地连通性**
```cmd
ping ************
```

**4. 检查路由表**
```cmd
route print | findstr "10.1.1"
```

**5. 测试路由连通性**
```cmd
ping ********
tracert ********
```

### 6. 调试工具和命令

#### 网络诊断命令
```cmd
# 显示所有网络接口
netsh interface show interface

# 显示接口详细信息
netsh interface ip show config

# 显示路由表
route print

# 显示 ARP 表
arp -a

# 网络连通性测试
ping -t ************
pathping ********
```

#### 日志和调试
```cmd
# 启用详细日志
set RUST_LOG=debug
tailscale-nat46.exe -config config.yaml

# 或使用测试程序
test-ip-config.exe
test-routing.exe
```

### 7. 配置文件检查

#### 验证 config.yaml
```yaml
# 确保配置正确
local_ipv4: "************"      # 虚拟接口 IP
local_ipv6: "fd7a:..."          # 您的 Tailscale IPv6 地址
mappings:
  "********": "fd7a:..."        # IPv4 -> IPv6 映射
interface_name: "tailscale-nat46"
log_level: "debug"              # 启用详细日志
```

#### 获取 Tailscale IPv6 地址
```cmd
# 在命令行中运行
tailscale ip -6
```

### 8. 常用修复脚本

#### 完全重置网络配置
```cmd
@echo off
echo 重置 tailscale-nat46 网络配置...

REM 删除所有相关路由
for %%i in (******** ******** ********) do (
    route delete %%i >nul 2>&1
)

REM 重置接口
netsh interface ip set address "tailscale-nat46" dhcp >nul 2>&1
netsh interface set interface "tailscale-nat46" admin=disable >nul 2>&1

echo 网络配置已重置
echo 请重新运行配置脚本
pause
```

### 9. 性能优化

#### 接口 MTU 设置
```cmd
# 设置合适的 MTU
netsh interface ipv4 set subinterface "tailscale-nat46" mtu=1420
```

#### 路由优先级
```cmd
# 使用低 metric 值提高路由优先级
route add ******** mask *************** ************ metric 1
```

### 10. 联系支持

如果问题仍然存在，请提供以下信息：

1. **系统信息**
   ```cmd
   systeminfo | findstr /B /C:"OS Name" /C:"OS Version"
   ```

2. **网络配置**
   ```cmd
   ipconfig /all > network-config.txt
   route print > route-table.txt
   ```

3. **错误日志**
   - 程序输出日志
   - Windows 事件查看器中的相关错误

4. **Tailscale 状态**
   ```cmd
   tailscale status
   tailscale ip -4
   tailscale ip -6
   ```

---

**提示**: 大多数问题都与权限和网络配置相关。确保以管理员身份运行所有操作，并按照本指南逐步排查。
