# 更新的HTTP代理解决方案

## 🔧 问题解决

监听特定IP地址失败的问题已解决！新方案使用端口转发。

## 📋 完整设置步骤

### 1. 设置端口转发（以管理员权限运行）

```cmd
build/setup-http-proxy.bat
```

这会设置：
- `********:80` → `127.0.0.1:8080`
- `********:80` → `127.0.0.1:8080`

### 2. 启动程序

```cmd
build/tailscale-nat46.exe -config config.yaml
```

程序现在会在端口8080监听，应该看到：
```
[INFO] Starting HTTP proxy...
[INFO] HTTP proxy started successfully
```

### 3. 测试访问

```
http://********/
```

## 🔍 工作流程

1. **浏览器请求** `http://********/`
2. **Windows端口转发** `********:80` → `127.0.0.1:8080`
3. **HTTP代理接收**请求并解析目标
4. **转发到IPv6** `http://[fd7a:115c:a1e0:b1a:0:7:c0a8:601]/`
5. **Tailscale路由**到远程机器
6. **响应返回**给浏览器

## 🔍 预期日志

访问时应该看到：
```
[INFO] HTTP request: GET / (Host: ********)
[INFO] Forwarding to IPv6: ******** -> http://[fd7a:115c:a1e0:b1a:0:7:c0a8:601]/
[INFO] Received response from IPv6: 200 OK
[INFO] Successfully forwarded response: 1234 bytes
```

## 🛠️ 故障排除

### 如果端口转发失败
- 确保以管理员权限运行setup脚本
- 检查端口8080是否被占用

### 如果程序启动失败
- 检查端口8080是否可用
- 确认config.yaml配置正确

### 如果转发失败
- 确认IPv6地址可直接访问
- 检查Tailscale状态

## 🧹 清理

如需清理配置：
```cmd
build/cleanup-http-proxy.bat
```

## 📊 验证设置

检查端口转发配置：
```cmd
netsh interface portproxy show all
```

应该显示：
```
Listen on ipv4:             Connect to ipv4:
Address         Port        Address         Port
********        80          127.0.0.1       8080
********        80          127.0.0.1       8080
```

这个方案应该能够完美工作！
