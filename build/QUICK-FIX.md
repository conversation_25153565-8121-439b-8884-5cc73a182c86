# 快速修复指南

## 问题诊断

你遇到的问题是路由没有正确添加。从你的路由表可以看出，缺少关键的路由规则。

## 解决步骤

### 1. 停止当前程序
首先停止正在运行的 `tailscale-nat46.exe`

### 2. 手动添加路由
以**管理员权限**运行 `add-routes-manual.bat`

或者手动执行以下命令：
```cmd
route add ******** mask *************** ************ metric 1
route add ******** mask *************** ************ metric 1
```

### 3. 验证路由
运行 `diagnose-nat46.bat` 检查路由是否正确添加

### 4. 重新启动程序
重新启动 `tailscale-nat46.exe`

### 5. 测试连接
尝试访问 `http://********`

## 预期的路由表

添加路由后，你应该在路由表中看到：
```
********  ***************  ************  ************  1
********  ***************  ************  ************  1
```

## 数据流向

正确配置后的数据流：
1. 浏览器访问 `http://********`
2. Windows路由将数据包发送到 `************` (Wintun接口)
3. tailscale-nat46 从Wintun接口接收IPv4数据包
4. 程序将IPv4数据包转换为IPv6
5. 转换后的IPv6数据包发送到 `fd7a:115c:a1e0:b1a:0:7:c0a8:601`
6. Tailscale将IPv6数据包路由到远程机器
7. 响应数据包按相反路径返回

## 故障排除

### 如果路由添加失败
- 确保以管理员权限运行
- 确保Wintun接口已启动并配置了IP地址

### 如果仍然无法访问
1. 检查Tailscale是否正常运行
2. 确认IPv6地址 `fd7a:115c:a1e0:b1a:0:7:c0a8:601` 可以直接访问
3. 检查防火墙设置
4. 查看程序日志输出

### 清理路由
如果需要清理路由，运行 `remove-routes-manual.bat`

## 程序改进

当前版本的程序在路由添加方面有一些限制。在下一个版本中，我们将：
1. 改进自动路由添加功能
2. 添加更好的错误处理
3. 实现真正的原始套接字支持

## 联系支持

如果问题仍然存在，请提供：
1. `diagnose-nat46.bat` 的输出
2. 程序的完整日志
3. Tailscale状态信息
