@echo off
chcp 65001 >nul
title 路由诊断工具

echo ========================================
echo tailscale-nat46 路由诊断工具
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限
    echo 请以管理员身份运行此脚本
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

echo 1. 显示所有网络接口
echo ========================================
netsh interface show interface
echo.

echo 2. 显示接口 IP 配置
echo ========================================
ipconfig /all | findstr /C:"适配器" /C:"IPv4" /C:"子网掩码" /C:"默认网关"
echo.

echo 3. 显示完整路由表
echo ========================================
route print
echo.

echo 4. 检查 tailscale-nat46 相关接口
echo ========================================
echo 查找包含 "nat46" 或 "test" 的接口:
netsh interface show interface | findstr /i "nat46 test wintun"
if %errorLevel% neq 0 (
    echo 未找到 tailscale-nat46 相关接口
) else (
    echo 找到相关接口
)
echo.

echo 5. 检查测试路由 (10.1.1.x)
echo ========================================
echo 查找 10.1.1.x 路由:
route print | findstr "10.1.1"
if %errorLevel% neq 0 (
    echo 未找到 10.1.1.x 路由
) else (
    echo 找到相关路由
)
echo.

echo 6. 检查 ************ 接口
echo ========================================
echo 查找配置了 ************ 的接口:
ipconfig /all | findstr /C:"************"
if %errorLevel% neq 0 (
    echo 未找到配置了 ************ 的接口
    echo 这可能是问题所在！
) else (
    echo ✅ 找到配置了 ************ 的接口
)
echo.

echo 7. 测试连通性
echo ========================================
echo 测试 ping ************:
ping -n 2 ************
echo.

echo 测试 ping ********:
ping -n 2 ********
echo.

echo 8. 路由分析
echo ========================================
echo 分析路由表中的问题...

REM 检查是否有到 10.1.1.x 的路由
route print | findstr "10.1.1" > temp_routes.txt
if exist temp_routes.txt (
    echo.
    echo 发现的 10.1.1.x 路由:
    type temp_routes.txt
    echo.
    
    REM 分析路由是否指向正确的接口
    findstr "************" temp_routes.txt > nul
    if %errorLevel% equ 0 (
        echo ✅ 路由网关正确 (************)
        
        REM 检查接口是否正确
        for /f "tokens=4" %%i in ('type temp_routes.txt') do (
            echo 路由接口: %%i
            ipconfig /all | findstr "%%i" > nul
            if %errorLevel% equ 0 (
                echo 检查接口 %%i 的配置:
                ipconfig /all | findstr /A:5 "%%i"
            )
        )
    ) else (
        echo ❌ 路由网关不正确 (应该是 ************)
    )
    
    del temp_routes.txt
) else (
    echo ❌ 未找到 10.1.1.x 路由
)

echo.
echo 9. 建议的修复步骤
echo ========================================

REM 检查是否有虚拟接口
netsh interface show interface | findstr /i "nat46 test wintun" > nul
if %errorLevel% neq 0 (
    echo 1. 首先创建虚拟接口:
    echo    test-wintun.exe 或 test-complete.exe
    echo.
)

REM 检查 IP 配置
ipconfig /all | findstr "************" > nul
if %errorLevel% neq 0 (
    echo 2. 配置虚拟接口 IP:
    echo    configure-ip.bat
    echo    或手动: netsh interface ip set address "接口名" static ************ *************
    echo.
)

REM 检查路由
route print | findstr "10.1.1" > nul
if %errorLevel% neq 0 (
    echo 3. 添加测试路由:
    echo    configure-routes.bat
    echo    或手动: route add ******** mask *************** ************ metric 1
    echo.
)

echo 4. 如果路由指向错误的接口:
echo    a. 删除错误的路由: route delete ********
echo    b. 确保虚拟接口 IP 正确配置
echo    c. 重新添加路由
echo.

echo 5. 运行完整测试:
echo    test-route-interface.exe
echo.

echo ========================================
echo 诊断完成
echo ========================================
echo.
echo 如果问题仍然存在，请:
echo 1. 检查上述输出中的错误信息
echo 2. 按照建议的修复步骤操作
echo 3. 参考 TROUBLESHOOTING.md 文档
echo.
pause
