@echo off
echo tailscale-nat46 Diagnostic Tool
echo ================================
echo.

echo 1. Checking network interfaces...
echo.
ipconfig | findstr /C:"Wintun" /C:"Tailscale" /C:"10.255.255" /C:"fd7a:"
echo.

echo 2. Checking IPv4 routes for mapped addresses...
echo.
route print | findstr "10.1.1"
echo.

echo 3. Checking IPv6 routes for Tailscale...
echo.
route print -6 | findstr "fd7a"
echo.

echo 4. Testing connectivity to Wintun interface...
echo.
ping -n 2 ************
echo.

echo 5. Checking if Tailscale is running...
echo.
tasklist | findstr "tailscale"
echo.

echo 6. Checking Tailscale status...
echo.
tailscale status 2>nul
if %errorLevel% neq 0 (
    echo Tailscale command not found or not running
)
echo.

echo 7. Testing IPv6 connectivity (if available)...
echo.
ping -6 -n 2 fd7a:115c:a1e0:b1a:0:7:c0a8:601 2>nul
if %errorLevel% neq 0 (
    echo IPv6 ping failed or address not reachable
)
echo.

echo 8. Current network configuration summary:
echo.
echo Expected configuration:
echo   - Wintun interface with IP ************
echo   - Route: ******** -> ************
echo   - Route: ******** -> ************
echo   - Tailscale running with IPv6 support
echo.

echo Diagnostic complete!
echo.
echo If routes are missing, run add-routes-manual.bat as Administrator
echo.
pause
