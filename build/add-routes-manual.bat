@echo off
echo Adding manual routes for tailscale-nat46...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Adding route for ******** via ************...
route add ******** mask *************** ************ metric 1

echo Adding route for ******** via ************...
route add ******** mask *************** ************ metric 1

echo.
echo Routes added successfully!
echo.
echo Current routes for mapped IPs:
route print | findstr "10.1.1"

echo.
echo You can now test accessing:
echo   http://********
echo   http://********
echo.
pause
