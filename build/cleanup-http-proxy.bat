@echo off
echo Cleaning up HTTP proxy configuration...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Removing port forwarding rules...
netsh interface portproxy delete v4tov4 listenaddress=******** listenport=80
netsh interface portproxy delete v4tov4 listenaddress=******** listenport=80

echo.
echo Cleanup complete!
echo.
pause
