@echo off
echo Fixing routes for tailscale-nat46...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Step 1: Removing existing incorrect routes...
route delete ******** 2>nul
route delete ******** 2>nul
echo.

echo Step 2: Adding correct routes to Wintun interface...
echo Adding route: ******** -> ************
route add ******** ************

echo Adding route: ******** -> ************  
route add ******** ************
echo.

echo Step 3: Verifying routes...
route print | findstr "10.1.1"
echo.

echo Routes fixed! The traffic should now go to the Wintun interface.
echo.
echo Test by accessing: http://********
echo.
pause
