//go:build windows

package main

import (
	"fmt"
	"net"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"

	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/wintun"
)

func main() {
	fmt.Println("Wintun IP Configuration Test")
	fmt.Println("===========================")

	// Check if running on Windows
	if runtime.GOOS != "windows" {
		fmt.Println("Error: This test only runs on Windows")
		os.Exit(1)
	}

	// Create logger
	log := logger.New("debug", "")

	// Test IP configuration
	testIP := "************"
	interfaceName := "test-ip-config"

	log.Info("Testing IP configuration for %s on interface %s", testIP, interfaceName)

	// Create Wintun interface configuration
	cfg := &wintun.Config{
		Name:    interfaceName,
		LocalIP: testIP,
		MTU:     1500,
		Logger:  log,
	}

	// Create interface
	log.Info("Creating Wintun interface...")
	iface, err := wintun.NewInterface(cfg)
	if err != nil {
		log.Error("Failed to create interface: %v", err)
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}

	// Start interface
	log.Info("Starting Wintun interface...")
	if err := iface.Start(); err != nil {
		log.Error("Failed to start interface: %v", err)
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}

	log.Info("Interface started successfully!")
	fmt.Printf("Interface: %s\n", iface.GetName())
	fmt.Printf("Configured IP: %s\n", iface.GetLocalIP().String())
	fmt.Printf("MTU: %d\n", iface.GetMTU())
	fmt.Println()

	// Wait a moment for the interface to be fully configured
	log.Info("Waiting for interface configuration to complete...")
	time.Sleep(3 * time.Second)

	// Check if the IP was actually configured
	log.Info("Checking actual interface configuration...")
	if err := checkInterfaceConfiguration(interfaceName, testIP, log); err != nil {
		log.Error("Interface configuration check failed: %v", err)
		
		// Try manual configuration
		log.Info("Attempting manual IP configuration...")
		if err := manualConfigureIP(interfaceName, testIP, log); err != nil {
			log.Error("Manual configuration failed: %v", err)
		} else {
			log.Info("Manual configuration successful!")
			
			// Check again
			time.Sleep(2 * time.Second)
			if err := checkInterfaceConfiguration(interfaceName, testIP, log); err != nil {
				log.Error("Configuration still not correct: %v", err)
			} else {
				log.Info("Configuration verified successfully!")
			}
		}
	} else {
		log.Info("Interface configuration verified successfully!")
	}

	fmt.Println()
	fmt.Println("Interface configuration test completed!")
	fmt.Println("You can now:")
	fmt.Printf("  1. Check interface status: ipconfig /all\n")
	fmt.Printf("  2. Ping the interface: ping %s\n", testIP)
	fmt.Printf("  3. View interface details: netsh interface show interface\n")
	fmt.Println()
	fmt.Println("Press Ctrl+C to stop and clean up...")

	// Set up signal handling
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signal
	<-sigChan
	log.Info("Shutdown signal received")

	// Stop interface
	log.Info("Stopping Wintun interface...")
	if err := iface.Stop(); err != nil {
		log.Error("Failed to stop interface: %v", err)
		os.Exit(1)
	}

	log.Info("Test completed successfully")
	fmt.Println("Interface stopped successfully")
}

// checkInterfaceConfiguration checks if the interface has the expected IP
func checkInterfaceConfiguration(interfaceName, expectedIP string, log *logger.Logger) error {
	interfaces, err := net.Interfaces()
	if err != nil {
		return fmt.Errorf("failed to get interfaces: %w", err)
	}

	for _, iface := range interfaces {
		if iface.Name == interfaceName {
			log.Debug("Found interface: %s", iface.Name)
			
			addrs, err := iface.Addrs()
			if err != nil {
				return fmt.Errorf("failed to get addresses for interface %s: %w", interfaceName, err)
			}

			log.Debug("Interface %s has %d addresses", interfaceName, len(addrs))
			for i, addr := range addrs {
				log.Debug("  Address %d: %s", i+1, addr.String())
				
				var ip net.IP
				switch v := addr.(type) {
				case *net.IPNet:
					ip = v.IP
				case *net.IPAddr:
					ip = v.IP
				}

				if ip != nil && ip.String() == expectedIP {
					log.Info("Found expected IP %s on interface %s", expectedIP, interfaceName)
					return nil
				}
			}

			return fmt.Errorf("expected IP %s not found on interface %s", expectedIP, interfaceName)
		}
	}

	return fmt.Errorf("interface %s not found", interfaceName)
}

// manualConfigureIP manually configures IP using netsh
func manualConfigureIP(interfaceName, ipAddr string, log *logger.Logger) error {
	log.Info("Manually configuring IP %s on interface %s using netsh", ipAddr, interfaceName)
	
	// Use netsh to configure the IP address
	cmd := fmt.Sprintf(`netsh interface ip set address "%s" static %s *************`, interfaceName, ipAddr)
	log.Debug("Executing command: %s", cmd)
	
	// Execute the command
	// Note: In a real implementation, you would use exec.Command
	// For now, just log what we would do
	log.Info("Would execute: %s", cmd)
	log.Warn("Manual configuration not fully implemented - please run manually:")
	log.Warn("  %s", cmd)
	
	return nil
}
