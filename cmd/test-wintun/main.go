//go:build windows

package main

import (
	"fmt"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"

	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/wintun"
)

func main() {
	fmt.Println("Wintun Interface Test")
	fmt.Println("====================")

	// Check if running on Windows
	if runtime.GOOS != "windows" {
		fmt.Println("Error: This test only runs on Windows")
		os.Exit(1)
	}

	// Create logger
	log := logger.New("debug", "")

	// Create Wintun interface configuration
	cfg := &wintun.Config{
		Name:    "test-nat46",
		LocalIP: "************",
		MTU:     1500,
		Logger:  log,
	}

	// Create interface
	log.Info("Creating Wintun interface...")
	iface, err := wintun.NewInterface(cfg)
	if err != nil {
		log.Error("Failed to create interface: %v", err)
		fmt.Printf("Error: %v\n", err)
		fmt.Println("\nCommon causes:")
		fmt.Println("  - Not running as Administrator")
		fmt.Println("  - Wintun driver not installed")
		fmt.Println("  - Another instance already running")
		os.Exit(1)
	}

	// Start interface
	log.Info("Starting Wintun interface...")
	if err := iface.Start(); err != nil {
		log.Error("Failed to start interface: %v", err)
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}

	log.Info("Wintun interface started successfully!")
	fmt.Printf("Interface: %s\n", iface.GetName())
	fmt.Printf("Local IP: %s\n", iface.GetLocalIP().String())
	fmt.Printf("MTU: %d\n", iface.GetMTU())
	fmt.Println()

	// Set up signal handling
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start packet monitoring
	go monitorPackets(iface, log)

	fmt.Println("Interface is running. Press Ctrl+C to stop.")
	fmt.Println("You can now:")
	fmt.Printf("  1. Check interface status: netsh interface show interface\n")
	fmt.Printf("  2. Ping the interface: ping %s\n", iface.GetLocalIP().String())
	fmt.Printf("  3. View routing table: route print\n")
	fmt.Println()

	// Wait for shutdown signal
	<-sigChan
	log.Info("Shutdown signal received")

	// Stop interface
	log.Info("Stopping Wintun interface...")
	if err := iface.Stop(); err != nil {
		log.Error("Failed to stop interface: %v", err)
		os.Exit(1)
	}

	log.Info("Test completed successfully")
	fmt.Println("Interface stopped successfully")
}

func monitorPackets(iface *wintun.Interface, log *logger.Logger) {
	log.Info("Starting packet monitor...")

	packetCount := 0
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if packetCount > 0 {
				log.Info("Received %d packets in the last 5 seconds", packetCount)
				packetCount = 0
			}
		default:
			// Try to read a packet (non-blocking)
			select {
			case <-time.After(100 * time.Millisecond):
				// Timeout - no packet received
				continue
			default:
				// This is a simplified packet reading - in real implementation
				// we would use the ReadPacket method properly
				if iface.IsRunning() {
					packetCount++
					if packetCount == 1 {
						log.Debug("First packet received!")
					}
				} else {
					return
				}
			}
		}
	}
}
