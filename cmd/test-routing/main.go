//go:build windows

package main

import (
	"fmt"
	"net"
	"os"
	"os/signal"
	"runtime"
	"strings"
	"syscall"

	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/routing"
)

func main() {
	fmt.Println("Routing Manager Test")
	fmt.Println("===================")

	// Check if running on Windows
	if runtime.GOOS != "windows" {
		fmt.Println("Error: This test only runs on Windows")
		os.Exit(1)
	}

	// Create logger
	log := logger.New("debug", "")

	// Create routing manager
	log.Info("Creating routing manager...")
	manager := routing.NewManager(log)

	// Test 1: Get default gateway
	log.Info("Testing default gateway detection...")
	gateway, err := routing.GetDefaultGateway()
	if err != nil {
		log.Error("Failed to get default gateway: %v", err)
	} else {
		log.Info("Default gateway: %s", gateway.String())
	}

	// Test 2: Get interface information
	log.Info("Testing interface enumeration...")
	interfaces, err := net.Interfaces()
	if err != nil {
		log.Error("Failed to get interfaces: %v", err)
	} else {
		log.Info("Found %d network interfaces:", len(interfaces))
		for i, iface := range interfaces {
			if i < 5 { // Show first 5 interfaces
				log.Info("  %d: %s (index: %d)", i+1, iface.Name, iface.Index)
			}
		}
	}

	// Test 3: Add test routes
	log.Info("Testing route management...")

	// Define test routes - these should use a virtual interface as gateway
	testRoutes := []struct {
		dest string
		desc string
	}{
		{"********", "Test host route 1"},
		{"********", "Test host route 2"},
		{"********", "Test host route 3"},
	}

	// Look for our virtual interface first (tailscale-nat46 or test-nat46)
	var testInterface uint32 = 1         // Default to loopback
	var testGateway string = "127.0.0.1" // Default gateway
	var foundVirtualInterface bool = false

	// First, try to find a virtual interface we created
	for _, iface := range interfaces {
		if strings.Contains(strings.ToLower(iface.Name), "nat46") ||
			strings.Contains(strings.ToLower(iface.Name), "test") {

			addrs, err := iface.Addrs()
			if err != nil {
				continue
			}

			for _, addr := range addrs {
				var ifaceIP net.IP
				switch v := addr.(type) {
				case *net.IPNet:
					ifaceIP = v.IP
				case *net.IPAddr:
					ifaceIP = v.IP
				}

				if ifaceIP != nil && ifaceIP.To4() != nil && !ifaceIP.IsLoopback() {
					// Found our virtual interface with IP
					testInterface = uint32(iface.Index)
					testGateway = ifaceIP.String()
					foundVirtualInterface = true
					log.Info("Found virtual interface: %s (index: %d, IP: %s)", iface.Name, iface.Index, ifaceIP.String())
					goto found
				}
			}
		}
	}

	// If no virtual interface found, use any interface with a valid IP
	if !foundVirtualInterface {
		for _, iface := range interfaces {
			addrs, err := iface.Addrs()
			if err != nil {
				continue
			}

			for _, addr := range addrs {
				var ifaceIP net.IP
				switch v := addr.(type) {
				case *net.IPNet:
					ifaceIP = v.IP
				case *net.IPAddr:
					ifaceIP = v.IP
				}

				if ifaceIP != nil && ifaceIP.To4() != nil && !ifaceIP.IsLoopback() {
					testInterface = uint32(iface.Index)
					testGateway = ifaceIP.String()
					log.Info("Using fallback interface: %s (index: %d, IP: %s)", iface.Name, iface.Index, ifaceIP.String())
					goto found
				}
			}
		}
	}

found:
	if !foundVirtualInterface {
		log.Warn("No virtual interface found. Routes may fail.")
		log.Info("Please run test-wintun.exe first to create a virtual interface")
	}

	// Add test routes using the found gateway
	addedRoutes := []net.IP{}
	for _, route := range testRoutes {
		dest := net.ParseIP(route.dest)
		gw := net.ParseIP(testGateway) // Use the gateway we found

		if dest == nil || gw == nil {
			log.Error("Invalid IP addresses: dest=%s, gw=%s", route.dest, testGateway)
			continue
		}

		log.Info("Adding route: %s", route.desc)
		err := manager.AddHostRoute(dest, gw, testInterface, 1)
		if err != nil {
			log.Error("Failed to add route %s: %v", route.dest, err)
		} else {
			log.Info("Successfully added route to %s", route.dest)
			addedRoutes = append(addedRoutes, dest)
		}
	}

	// Show managed routes
	routes := manager.GetManagedRoutes()
	log.Info("Currently managing %d routes:", len(routes))
	for key, route := range routes {
		log.Info("  %s: %s", key, routing.FormatRoute(route))
	}

	// Test 4: Route existence check
	log.Info("Testing route existence checks...")
	for _, dest := range addedRoutes {
		mask := net.CIDRMask(32, 32)
		exists, err := routing.CheckRouteExists(dest, mask)
		if err != nil {
			log.Error("Failed to check route existence for %s: %v", dest.String(), err)
		} else {
			log.Info("Route to %s exists: %t", dest.String(), exists)
		}
	}

	// Wait for user input or signal
	fmt.Println()
	fmt.Println("Routes added successfully!")
	fmt.Println("You can now:")
	fmt.Println("  1. Check routing table: route print")
	fmt.Println("  2. Test connectivity: ping ********")
	fmt.Println("  3. View interface status: netsh interface show interface")
	fmt.Println()
	fmt.Println("Press Ctrl+C to clean up and exit...")

	// Set up signal handling
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signal
	<-sigChan
	log.Info("Shutdown signal received")

	// Test 5: Clean up routes
	log.Info("Cleaning up routes...")
	err = manager.RemoveAllRoutes()
	if err != nil {
		log.Error("Failed to remove all routes: %v", err)
	} else {
		log.Info("All routes removed successfully")
	}

	// Verify cleanup
	remainingRoutes := manager.GetRouteCount()
	if remainingRoutes > 0 {
		log.Warn("Warning: %d routes still managed after cleanup", remainingRoutes)
	} else {
		log.Info("Cleanup verified: no routes remaining")
	}

	log.Info("Test completed successfully")
	fmt.Println("Test completed!")
}

// Helper function to demonstrate route validation
func demonstrateValidation(log *logger.Logger) {
	log.Info("Demonstrating route validation...")

	testCases := []struct {
		dest      string
		mask      string
		gateway   string
		iface     uint32
		shouldErr bool
		desc      string
	}{
		{"********", "***************", "************", 1, false, "Valid host route"},
		{"", "***************", "************", 1, true, "Empty destination"},
		{"********", "***************", "", 1, true, "Empty gateway"},
		{"********", "***************", "************", 0, true, "Zero interface"},
		{"invalid", "***************", "************", 1, true, "Invalid destination IP"},
		{"********", "***************", "invalid", 1, true, "Invalid gateway IP"},
	}

	for i, tc := range testCases {
		dest := net.ParseIP(tc.dest)
		gw := net.ParseIP(tc.gateway)
		mask := net.IPMask(net.ParseIP(tc.mask).To4())

		err := routing.ValidateRoute(dest, mask, gw, tc.iface)
		hasErr := err != nil

		if hasErr == tc.shouldErr {
			log.Info("Test %d PASS: %s", i+1, tc.desc)
		} else {
			log.Error("Test %d FAIL: %s (expected error: %t, got error: %t)",
				i+1, tc.desc, tc.shouldErr, hasErr)
			if err != nil {
				log.Error("  Error: %v", err)
			}
		}
	}
}
