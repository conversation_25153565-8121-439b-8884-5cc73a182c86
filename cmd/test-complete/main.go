//go:build windows

package main

import (
	"fmt"
	"net"
	"os"
	"os/signal"
	"runtime"
	"strings"
	"syscall"
	"time"

	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/routing"
	"tailscale-nat46/pkg/wintun"
)

func main() {
	fmt.Println("tailscale-nat46 Complete Test")
	fmt.Println("=============================")

	// Check if running on Windows
	if runtime.GOOS != "windows" {
		fmt.Println("Error: This test only runs on Windows")
		os.Exit(1)
	}

	// Create logger
	log := logger.New("info", "")

	// Test configuration
	interfaceName := "test-complete"
	interfaceIP := "************"
	testRoutes := []string{"********", "********", "********"}

	log.Info("Starting complete test with interface %s (IP: %s)", interfaceName, interfaceIP)

	// Step 1: Create and configure Wintun interface
	log.Info("Step 1: Creating Wintun interface...")
	wintunCfg := &wintun.Config{
		Name:    interfaceName,
		LocalIP: interfaceIP,
		MTU:     1500,
		Logger:  log,
	}

	iface, err := wintun.NewInterface(wintunCfg)
	if err != nil {
		log.Error("Failed to create interface: %v", err)
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}

	if err := iface.Start(); err != nil {
		log.Error("Failed to start interface: %v", err)
		fmt.Printf("Error: %v\n", err)
		os.Exit(1)
	}

	log.Info("✅ Wintun interface created and started")

	// Wait for interface to be ready
	log.Info("Waiting for interface to be fully configured...")
	time.Sleep(5 * time.Second)

	// Step 2: Verify interface configuration
	log.Info("Step 2: Verifying interface configuration...")
	if err := verifyInterfaceConfig(interfaceName, interfaceIP, log); err != nil {
		log.Warn("Interface verification failed: %v", err)
		log.Info("This may be normal - continuing with tests...")
	} else {
		log.Info("✅ Interface configuration verified")
	}

	// Step 3: Create routing manager and add routes
	log.Info("Step 3: Testing route management...")
	routingMgr := routing.NewManager(log)

	// Find the interface we just created - try multiple times
	var interfaceIndex uint32
	var findErr error
	for i := 0; i < 5; i++ {
		interfaceIndex, findErr = findInterfaceIndex(interfaceName, log)
		if findErr == nil {
			log.Info("✅ Found interface %s with index %d", interfaceName, interfaceIndex)
			break
		}
		log.Warn("Attempt %d: Failed to find interface %s: %v", i+1, interfaceName, findErr)
		if i < 4 {
			time.Sleep(2 * time.Second)
		}
	}

	if findErr != nil {
		log.Error("Failed to find interface %s after 5 attempts", interfaceName)
		log.Info("Available interfaces:")
		listAllInterfaces(log)
		log.Error("Cannot proceed with route testing without correct interface")
		return
	}

	gatewayIP := net.ParseIP(interfaceIP)
	if gatewayIP == nil {
		log.Error("Invalid gateway IP: %s", interfaceIP)
		os.Exit(1)
	}

	// Add test routes
	successCount := 0
	for _, destStr := range testRoutes {
		destIP := net.ParseIP(destStr)
		if destIP == nil {
			log.Error("Invalid destination IP: %s", destStr)
			continue
		}

		log.Info("Adding route: %s -> %s", destStr, interfaceIP)
		err := routingMgr.AddHostRoute(destIP, gatewayIP, interfaceIndex, 1)
		if err != nil {
			log.Error("Failed to add route %s: %v", destStr, err)
		} else {
			log.Info("✅ Route added successfully: %s", destStr)
			successCount++
		}
	}

	log.Info("Route management test completed: %d/%d routes added successfully", successCount, len(testRoutes))

	// Step 4: Display current configuration
	log.Info("Step 4: Displaying current configuration...")
	displayConfiguration(interfaceName, routingMgr, log)

	// Step 5: Interactive testing
	fmt.Println()
	fmt.Println("=== Test Results ===")
	fmt.Printf("Interface: %s\n", interfaceName)
	fmt.Printf("IP Address: %s\n", interfaceIP)
	fmt.Printf("Routes Added: %d/%d\n", successCount, len(testRoutes))
	fmt.Println()
	fmt.Println("You can now test:")
	fmt.Printf("  1. Ping interface: ping %s\n", interfaceIP)
	for _, route := range testRoutes {
		fmt.Printf("  2. Test route: ping %s\n", route)
	}
	fmt.Println("  3. Check routing table: route print")
	fmt.Println("  4. Check interface: ipconfig /all")
	fmt.Println()
	fmt.Println("Press Ctrl+C to clean up and exit...")

	// Set up signal handling
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signal
	<-sigChan
	log.Info("Shutdown signal received")

	// Step 6: Cleanup
	log.Info("Step 6: Cleaning up...")

	// Remove routes
	log.Info("Removing routes...")
	if err := routingMgr.RemoveAllRoutes(); err != nil {
		log.Error("Failed to remove routes: %v", err)
	} else {
		log.Info("✅ Routes removed")
	}

	// Stop interface
	log.Info("Stopping interface...")
	if err := iface.Stop(); err != nil {
		log.Error("Failed to stop interface: %v", err)
	} else {
		log.Info("✅ Interface stopped")
	}

	log.Info("Complete test finished successfully")
	fmt.Println("Test completed!")
}

// verifyInterfaceConfig checks if the interface has the expected IP
func verifyInterfaceConfig(interfaceName, expectedIP string, log *logger.Logger) error {
	interfaces, err := net.Interfaces()
	if err != nil {
		return fmt.Errorf("failed to get interfaces: %w", err)
	}

	for _, iface := range interfaces {
		if iface.Name == interfaceName {
			addrs, err := iface.Addrs()
			if err != nil {
				return fmt.Errorf("failed to get addresses: %w", err)
			}

			for _, addr := range addrs {
				var ip net.IP
				switch v := addr.(type) {
				case *net.IPNet:
					ip = v.IP
				case *net.IPAddr:
					ip = v.IP
				}

				if ip != nil && ip.String() == expectedIP {
					log.Debug("Found expected IP %s on interface %s", expectedIP, interfaceName)
					return nil
				}
			}

			return fmt.Errorf("expected IP %s not found on interface %s", expectedIP, interfaceName)
		}
	}

	return fmt.Errorf("interface %s not found", interfaceName)
}

// findInterfaceIndex finds the index of the specified interface
func findInterfaceIndex(interfaceName string, log *logger.Logger) (uint32, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return 0, fmt.Errorf("failed to get interfaces: %w", err)
	}

	for _, iface := range interfaces {
		if iface.Name == interfaceName {
			log.Debug("Found interface %s with index %d", interfaceName, iface.Index)
			return uint32(iface.Index), nil
		}
	}

	return 0, fmt.Errorf("interface %s not found", interfaceName)
}

// listAllInterfaces lists all network interfaces for debugging
func listAllInterfaces(log *logger.Logger) {
	interfaces, err := net.Interfaces()
	if err != nil {
		log.Error("Failed to get interfaces: %v", err)
		return
	}

	for _, iface := range interfaces {
		log.Info("  Interface: %s (index: %d, flags: %v)", iface.Name, iface.Index, iface.Flags)

		addrs, err := iface.Addrs()
		if err != nil {
			log.Error("    Failed to get addresses: %v", err)
			continue
		}

		for _, addr := range addrs {
			log.Info("    Address: %s", addr.String())
		}
	}
}

// displayConfiguration shows the current network configuration
func displayConfiguration(interfaceName string, routingMgr *routing.Manager, log *logger.Logger) {
	log.Info("Current network configuration:")

	// Show interface details
	interfaces, err := net.Interfaces()
	if err != nil {
		log.Error("Failed to get interfaces: %v", err)
		return
	}

	for _, iface := range interfaces {
		if strings.Contains(strings.ToLower(iface.Name), "nat46") ||
			strings.Contains(strings.ToLower(iface.Name), "test") ||
			iface.Name == interfaceName {

			log.Info("Interface: %s (index: %d)", iface.Name, iface.Index)

			addrs, err := iface.Addrs()
			if err != nil {
				log.Error("  Failed to get addresses: %v", err)
				continue
			}

			for _, addr := range addrs {
				log.Info("  Address: %s", addr.String())
			}
		}
	}

	// Show managed routes
	routes := routingMgr.GetManagedRoutes()
	log.Info("Managed routes (%d):", len(routes))
	for key, route := range routes {
		log.Info("  %s: %s", key, routing.FormatRoute(route))
	}
}
