//go:build windows

package main

import (
	"fmt"
	"net"
	"os"
	"os/exec"
	"runtime"
	"strings"
	"time"

	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/routing"
	"tailscale-nat46/pkg/wintun"
)

func main() {
	fmt.Println("Route Interface Test")
	fmt.Println("===================")

	// Check if running on Windows
	if runtime.GOOS != "windows" {
		fmt.Println("Error: This test only runs on Windows")
		os.Exit(1)
	}

	// Create logger
	log := logger.New("debug", "")

	// Test configuration
	interfaceName := "test-route-iface"
	interfaceIP := "************"
	testDestination := "**********"

	log.Info("Testing route to correct interface")
	log.Info("Interface: %s, IP: %s", interfaceName, interfaceIP)
	log.Info("Test destination: %s", testDestination)

	// Step 1: Create Wintun interface
	log.Info("Step 1: Creating Wintun interface...")
	wintunCfg := &wintun.Config{
		Name:    interfaceName,
		LocalIP: interfaceIP,
		MTU:     1500,
		Logger:  log,
	}

	iface, err := wintun.NewInterface(wintunCfg)
	if err != nil {
		log.Error("Failed to create interface: %v", err)
		os.Exit(1)
	}

	if err := iface.Start(); err != nil {
		log.Error("Failed to start interface: %v", err)
		os.Exit(1)
	}

	log.Info("✅ Interface created and started")

	// Wait for interface to be ready
	time.Sleep(5 * time.Second)

	// Step 2: Find the interface index
	log.Info("Step 2: Finding interface index...")
	interfaceIndex, err := findInterfaceIndex(interfaceName, log)
	if err != nil {
		log.Error("Failed to find interface: %v", err)
		log.Info("Available interfaces:")
		listInterfaces(log)
		iface.Stop()
		os.Exit(1)
	}

	log.Info("✅ Found interface %s with index %d", interfaceName, interfaceIndex)

	// Step 3: Show current routing table
	log.Info("Step 3: Current routing table before adding route...")
	showRoutingTable(testDestination, log)

	// Step 4: Add route using our routing manager
	log.Info("Step 4: Adding route using routing manager...")
	routingMgr := routing.NewManager(log)
	
	destIP := net.ParseIP(testDestination)
	gatewayIP := net.ParseIP(interfaceIP)
	
	err = routingMgr.AddHostRoute(destIP, gatewayIP, interfaceIndex, 1)
	if err != nil {
		log.Error("Failed to add route via routing manager: %v", err)
		
		// Try manual route command
		log.Info("Trying manual route command...")
		err = addRouteManually(testDestination, interfaceIP, log)
		if err != nil {
			log.Error("Manual route command also failed: %v", err)
		}
	} else {
		log.Info("✅ Route added successfully via routing manager")
	}

	// Step 5: Verify the route
	log.Info("Step 5: Verifying route...")
	time.Sleep(2 * time.Second)
	showRoutingTable(testDestination, log)

	// Step 6: Test the route
	log.Info("Step 6: Testing route connectivity...")
	testRoute(testDestination, log)

	fmt.Println()
	fmt.Println("=== Test Summary ===")
	fmt.Printf("Interface: %s (index: %d)\n", interfaceName, interfaceIndex)
	fmt.Printf("Gateway IP: %s\n", interfaceIP)
	fmt.Printf("Test destination: %s\n", testDestination)
	fmt.Println()
	fmt.Println("Check the routing table output above to verify:")
	fmt.Printf("- Route to %s should use gateway %s\n", testDestination, interfaceIP)
	fmt.Printf("- Route should be on interface with IP %s (not physical interface)\n", interfaceIP)
	fmt.Println()
	fmt.Println("Press Enter to clean up and exit...")
	fmt.Scanln()

	// Cleanup
	log.Info("Cleaning up...")
	routingMgr.RemoveAllRoutes()
	iface.Stop()
	log.Info("Cleanup completed")
}

// findInterfaceIndex finds the index of the specified interface
func findInterfaceIndex(interfaceName string, log *logger.Logger) (uint32, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return 0, fmt.Errorf("failed to get interfaces: %w", err)
	}

	for _, iface := range interfaces {
		if iface.Name == interfaceName {
			log.Debug("Found interface %s with index %d", interfaceName, iface.Index)
			return uint32(iface.Index), nil
		}
	}

	return 0, fmt.Errorf("interface %s not found", interfaceName)
}

// listInterfaces lists all network interfaces
func listInterfaces(log *logger.Logger) {
	interfaces, err := net.Interfaces()
	if err != nil {
		log.Error("Failed to get interfaces: %v", err)
		return
	}

	for _, iface := range interfaces {
		log.Info("  %s (index: %d)", iface.Name, iface.Index)
		
		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}

		for _, addr := range addrs {
			log.Info("    %s", addr.String())
		}
	}
}

// showRoutingTable shows relevant entries from the routing table
func showRoutingTable(destination string, log *logger.Logger) {
	log.Info("Routing table entries for %s:", destination)
	
	cmd := exec.Command("route", "print")
	output, err := cmd.Output()
	if err != nil {
		log.Error("Failed to get routing table: %v", err)
		return
	}

	lines := strings.Split(string(output), "\n")
	found := false
	
	for _, line := range lines {
		if strings.Contains(line, destination) {
			log.Info("  %s", strings.TrimSpace(line))
			found = true
		}
	}
	
	if !found {
		log.Info("  No routes found for %s", destination)
	}
	
	// Also show interface list from route print
	log.Info("Interface list:")
	inInterfaceList := false
	for _, line := range lines {
		if strings.Contains(line, "接口列表") || strings.Contains(line, "Interface List") {
			inInterfaceList = true
			continue
		}
		if strings.Contains(line, "===========") && inInterfaceList {
			inInterfaceList = false
			continue
		}
		if inInterfaceList && strings.TrimSpace(line) != "" {
			log.Info("  %s", strings.TrimSpace(line))
		}
	}
}

// addRouteManually adds a route using the route command directly
func addRouteManually(destination, gateway string, log *logger.Logger) error {
	args := []string{"add", destination, "mask", "255.255.255.255", gateway, "metric", "1"}
	log.Info("Executing: route %v", args)
	
	cmd := exec.Command("route", args...)
	output, err := cmd.CombinedOutput()
	
	log.Info("Route command output: %s", string(output))
	
	if err != nil {
		return fmt.Errorf("route command failed: %v", err)
	}
	
	return nil
}

// testRoute tests connectivity to the destination
func testRoute(destination string, log *logger.Logger) {
	log.Info("Testing ping to %s...", destination)
	
	cmd := exec.Command("ping", "-n", "2", destination)
	output, err := cmd.CombinedOutput()
	
	if err != nil {
		log.Warn("Ping failed (this is expected): %v", err)
	}
	
	// Show ping output for analysis
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.TrimSpace(line) != "" {
			log.Info("  %s", strings.TrimSpace(line))
		}
	}
}
