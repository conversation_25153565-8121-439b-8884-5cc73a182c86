//go:build windows

package main

import (
	"fmt"
	"net"
	"os"
	"runtime"

	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/netstack"
	"tailscale-nat46/pkg/translator"
)

func main() {
	fmt.Println("IPv4 to IPv6 Translation Test")
	fmt.Println("=============================")

	// Check if running on Windows
	if runtime.GOOS != "windows" {
		fmt.Println("Error: This test only runs on Windows")
		os.Exit(1)
	}

	// Create logger
	log := logger.New("debug", "")

	// Test configuration
	localIPv4 := "************"
	localIPv6 := "fd7a:115c:a1e0:b1a:0:7:a8c0:101"
	mappings := map[string]string{
		"********": "fd7a:115c:a1e0:b1a:0:7:c0a8:601",
		"********": "fd7a:115c:a1e0:b1a:0:7:c0a8:602",
	}

	// Create network stack
	netstackCfg := &netstack.Config{
		LocalIPv4: localIPv4,
		LocalIPv6: localIPv6,
		Logger:    log,
	}

	netStack, err := netstack.NewStack(netstackCfg)
	if err != nil {
		fmt.Printf("Failed to create network stack: %v\n", err)
		os.Exit(1)
	}

	if err := netStack.Start(); err != nil {
		fmt.Printf("Failed to start network stack: %v\n", err)
		os.Exit(1)
	}
	defer netStack.Stop()

	// Create translator
	translatorCfg := &translator.Config{
		LocalIPv4: localIPv4,
		LocalIPv6: localIPv6,
		Mappings:  mappings,
		Logger:    log,
		NetStack:  netStack,
	}

	trans, err := translator.NewTranslator(translatorCfg)
	if err != nil {
		fmt.Printf("Failed to create translator: %v\n", err)
		os.Exit(1)
	}

	if err := trans.Start(); err != nil {
		fmt.Printf("Failed to start translator: %v\n", err)
		os.Exit(1)
	}
	defer trans.Stop()

	fmt.Printf("Network stack and translator initialized successfully\n")
	fmt.Printf("Local IPv4: %s\n", localIPv4)
	fmt.Printf("Local IPv6: %s\n", localIPv6)
	fmt.Printf("Mappings: %d\n", len(mappings))

	// Test 1: TCP packet translation
	fmt.Println("\n--- Test 1: TCP Packet Translation ---")
	if err := testTCPTranslation(netStack, trans); err != nil {
		fmt.Printf("TCP translation test failed: %v\n", err)
	} else {
		fmt.Println("TCP translation test passed!")
	}

	// Test 2: UDP packet translation
	fmt.Println("\n--- Test 2: UDP Packet Translation ---")
	if err := testUDPTranslation(netStack, trans); err != nil {
		fmt.Printf("UDP translation test failed: %v\n", err)
	} else {
		fmt.Println("UDP translation test passed!")
	}

	// Test 3: ICMP packet translation
	fmt.Println("\n--- Test 3: ICMP Packet Translation ---")
	if err := testICMPTranslation(netStack, trans); err != nil {
		fmt.Printf("ICMP translation test failed: %v\n", err)
	} else {
		fmt.Println("ICMP translation test passed!")
	}

	fmt.Println("\n--- All Tests Completed ---")
}

func testTCPTranslation(netStack *netstack.Stack, trans *translator.Translator) error {
	// Create a sample TCP packet
	srcIP := net.ParseIP("*************")
	dstIP := net.ParseIP("********")
	
	// Build TCP payload
	tcpPayload := netStack.BuildTCPResponse(12345, 80, 1000, 0, 0x02, []byte("Hello TCP"))
	
	// Build IPv4 packet
	ipv4Packet := netStack.BuildIPv4Response(srcIP, dstIP, netstack.ProtocolTCP, tcpPayload)
	
	// Parse the packet
	packet, err := netStack.ProcessInboundPacket(ipv4Packet)
	if err != nil {
		return fmt.Errorf("failed to parse IPv4 packet: %w", err)
	}
	
	// Translate to IPv6
	ipv6Packet, err := trans.TranslateIPv4ToIPv6(packet)
	if err != nil {
		return fmt.Errorf("failed to translate to IPv6: %w", err)
	}
	
	// Parse the IPv6 packet to verify
	ipv6ParsedPacket, err := netStack.ProcessInboundPacket(ipv6Packet)
	if err != nil {
		return fmt.Errorf("failed to parse translated IPv6 packet: %w", err)
	}
	
	// Verify the translation
	if !ipv6ParsedPacket.IsIPv6 {
		return fmt.Errorf("translated packet is not IPv6")
	}
	
	if ipv6ParsedPacket.IPv6Header.NextHeader != netstack.ProtocolTCP {
		return fmt.Errorf("protocol mismatch: expected TCP, got %d", ipv6ParsedPacket.IPv6Header.NextHeader)
	}
	
	fmt.Printf("  Original IPv4: %s -> %s\n", srcIP.String(), dstIP.String())
	fmt.Printf("  Translated IPv6: %s -> %s\n", 
		ipv6ParsedPacket.IPv6Header.SrcIP.String(), 
		ipv6ParsedPacket.IPv6Header.DstIP.String())
	
	return nil
}

func testUDPTranslation(netStack *netstack.Stack, trans *translator.Translator) error {
	// Create a sample UDP packet
	srcIP := net.ParseIP("*************")
	dstIP := net.ParseIP("********")
	
	// Build UDP payload
	udpPayload := netStack.BuildUDPResponse(12345, 53, []byte("Hello UDP"))
	
	// Build IPv4 packet
	ipv4Packet := netStack.BuildIPv4Response(srcIP, dstIP, netstack.ProtocolUDP, udpPayload)
	
	// Parse the packet
	packet, err := netStack.ProcessInboundPacket(ipv4Packet)
	if err != nil {
		return fmt.Errorf("failed to parse IPv4 packet: %w", err)
	}
	
	// Translate to IPv6
	ipv6Packet, err := trans.TranslateIPv4ToIPv6(packet)
	if err != nil {
		return fmt.Errorf("failed to translate to IPv6: %w", err)
	}
	
	// Parse the IPv6 packet to verify
	ipv6ParsedPacket, err := netStack.ProcessInboundPacket(ipv6Packet)
	if err != nil {
		return fmt.Errorf("failed to parse translated IPv6 packet: %w", err)
	}
	
	// Verify the translation
	if !ipv6ParsedPacket.IsIPv6 {
		return fmt.Errorf("translated packet is not IPv6")
	}
	
	if ipv6ParsedPacket.IPv6Header.NextHeader != netstack.ProtocolUDP {
		return fmt.Errorf("protocol mismatch: expected UDP, got %d", ipv6ParsedPacket.IPv6Header.NextHeader)
	}
	
	fmt.Printf("  Original IPv4: %s -> %s\n", srcIP.String(), dstIP.String())
	fmt.Printf("  Translated IPv6: %s -> %s\n", 
		ipv6ParsedPacket.IPv6Header.SrcIP.String(), 
		ipv6ParsedPacket.IPv6Header.DstIP.String())
	
	return nil
}

func testICMPTranslation(netStack *netstack.Stack, trans *translator.Translator) error {
	// Create a sample ICMP packet
	srcIP := net.ParseIP("*************")
	dstIP := net.ParseIP("********")
	
	// Build ICMP payload (ping)
	icmpPayload := netStack.BuildICMPResponse(8, 0, 1234, 1, []byte("Hello ICMP"))
	
	// Build IPv4 packet
	ipv4Packet := netStack.BuildIPv4Response(srcIP, dstIP, netstack.ProtocolICMP, icmpPayload)
	
	// Parse the packet
	packet, err := netStack.ProcessInboundPacket(ipv4Packet)
	if err != nil {
		return fmt.Errorf("failed to parse IPv4 packet: %w", err)
	}
	
	// Translate to IPv6
	ipv6Packet, err := trans.TranslateIPv4ToIPv6(packet)
	if err != nil {
		return fmt.Errorf("failed to translate to IPv6: %w", err)
	}
	
	// Parse the IPv6 packet to verify
	ipv6ParsedPacket, err := netStack.ProcessInboundPacket(ipv6Packet)
	if err != nil {
		return fmt.Errorf("failed to parse translated IPv6 packet: %w", err)
	}
	
	// Verify the translation
	if !ipv6ParsedPacket.IsIPv6 {
		return fmt.Errorf("translated packet is not IPv6")
	}
	
	if ipv6ParsedPacket.IPv6Header.NextHeader != netstack.ProtocolICMPv6 {
		return fmt.Errorf("protocol mismatch: expected ICMPv6, got %d", ipv6ParsedPacket.IPv6Header.NextHeader)
	}
	
	// Verify ICMP type translation (8 -> 128)
	if ipv6ParsedPacket.ICMPHeader.Type != 128 {
		return fmt.Errorf("ICMP type mismatch: expected 128, got %d", ipv6ParsedPacket.ICMPHeader.Type)
	}
	
	fmt.Printf("  Original IPv4: %s -> %s (ICMP type 8)\n", srcIP.String(), dstIP.String())
	fmt.Printf("  Translated IPv6: %s -> %s (ICMPv6 type 128)\n", 
		ipv6ParsedPacket.IPv6Header.SrcIP.String(), 
		ipv6ParsedPacket.IPv6Header.DstIP.String())
	
	return nil
}
