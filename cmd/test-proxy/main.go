//go:build windows

package main

import (
	"fmt"
	"net"
	"os"
	"runtime"
	"time"

	"tailscale-nat46/pkg/config"
	"tailscale-nat46/pkg/logger"
)

func main() {
	fmt.Println("Port Forwarding Proxy Test")
	fmt.Println("==========================")

	// Check if running on Windows
	if runtime.GOOS != "windows" {
		fmt.Println("Error: This test only runs on Windows")
		os.Exit(1)
	}

	// Create logger
	log := logger.New("info", "")

	// Test configuration loading
	log.Info("Testing configuration loading...")
	if err := testConfigLoading(); err != nil {
		log.Error("Configuration test failed: %v", err)
		os.Exit(1)
	}
	log.Info("Configuration test passed")

	// Test port availability
	log.Info("Testing port availability...")
	if err := testPortAvailability(); err != nil {
		log.Error("Port availability test failed: %v", err)
		os.Exit(1)
	}
	log.Info("Port availability test passed")

	// Test TCP connection
	log.Info("Testing TCP connection...")
	if err := testTCPConnection(); err != nil {
		log.Error("TCP connection test failed: %v", err)
		os.Exit(1)
	}
	log.Info("TCP connection test passed")

	// Test UDP connection
	log.Info("Testing UDP connection...")
	if err := testUDPConnection(); err != nil {
		log.Error("UDP connection test failed: %v", err)
		os.Exit(1)
	}
	log.Info("UDP connection test passed")

	fmt.Println("\nAll tests passed successfully!")
	fmt.Println("The proxy should be ready to use.")
}

func testConfigLoading() error {
	// Test default configuration
	cfg := config.DefaultConfig()
	if err := cfg.Validate(); err != nil {
		return fmt.Errorf("default config validation failed: %w", err)
	}

	// Test configuration with custom port mappings
	cfg.PortMappings = []config.PortMapping{
		{ListenPort: 8080, Protocol: "tcp"},
		{ListenPort: 53, Protocol: "udp"},
		{ListenPort: 80, Protocol: "both"},
	}

	if err := cfg.Validate(); err != nil {
		return fmt.Errorf("custom config validation failed: %w", err)
	}

	return nil
}

func testPortAvailability() error {
	// Test if common ports are available
	testPorts := []int{8080, 8081, 8082}

	for _, port := range testPorts {
		// Test TCP port
		addr := fmt.Sprintf(":%d", port)
		listener, err := net.Listen("tcp", addr)
		if err != nil {
			return fmt.Errorf("TCP port %d is not available: %w", port, err)
		}
		listener.Close()

		// Test UDP port
		udpAddr, err := net.ResolveUDPAddr("udp", addr)
		if err != nil {
			return fmt.Errorf("failed to resolve UDP address for port %d: %w", port, err)
		}

		udpConn, err := net.ListenUDP("udp", udpAddr)
		if err != nil {
			return fmt.Errorf("UDP port %d is not available: %w", port, err)
		}
		udpConn.Close()
	}

	return nil
}

func testTCPConnection() error {
	// Start a simple TCP echo server
	listener, err := net.Listen("tcp", ":8090")
	if err != nil {
		return fmt.Errorf("failed to start TCP echo server: %w", err)
	}
	defer listener.Close()

	// Start echo server in background
	go func() {
		for {
			conn, err := listener.Accept()
			if err != nil {
				return
			}
			go handleTCPEcho(conn)
		}
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Test connection
	conn, err := net.Dial("tcp", "localhost:8090")
	if err != nil {
		return fmt.Errorf("failed to connect to TCP echo server: %w", err)
	}
	defer conn.Close()

	// Send test message
	testMsg := "Hello, TCP!"
	_, err = conn.Write([]byte(testMsg))
	if err != nil {
		return fmt.Errorf("failed to send TCP message: %w", err)
	}

	// Read response
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return fmt.Errorf("failed to read TCP response: %w", err)
	}

	response := string(buffer[:n])
	if response != testMsg {
		return fmt.Errorf("TCP echo mismatch: expected %q, got %q", testMsg, response)
	}

	return nil
}

func testUDPConnection() error {
	// Start a simple UDP echo server
	udpAddr, err := net.ResolveUDPAddr("udp", ":8091")
	if err != nil {
		return fmt.Errorf("failed to resolve UDP address: %w", err)
	}

	udpConn, err := net.ListenUDP("udp", udpAddr)
	if err != nil {
		return fmt.Errorf("failed to start UDP echo server: %w", err)
	}
	defer udpConn.Close()

	// Start echo server in background
	go handleUDPEcho(udpConn)

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Test connection
	clientConn, err := net.Dial("udp", "localhost:8091")
	if err != nil {
		return fmt.Errorf("failed to connect to UDP echo server: %w", err)
	}
	defer clientConn.Close()

	// Send test message
	testMsg := "Hello, UDP!"
	_, err = clientConn.Write([]byte(testMsg))
	if err != nil {
		return fmt.Errorf("failed to send UDP message: %w", err)
	}

	// Read response
	buffer := make([]byte, 1024)
	clientConn.SetReadDeadline(time.Now().Add(5 * time.Second))
	n, err := clientConn.Read(buffer)
	if err != nil {
		return fmt.Errorf("failed to read UDP response: %w", err)
	}

	response := string(buffer[:n])
	if response != testMsg {
		return fmt.Errorf("UDP echo mismatch: expected %q, got %q", testMsg, response)
	}

	return nil
}

func handleTCPEcho(conn net.Conn) {
	defer conn.Close()
	buffer := make([]byte, 1024)
	
	for {
		n, err := conn.Read(buffer)
		if err != nil {
			return
		}
		
		_, err = conn.Write(buffer[:n])
		if err != nil {
			return
		}
	}
}

func handleUDPEcho(conn *net.UDPConn) {
	buffer := make([]byte, 1024)
	
	for {
		n, addr, err := conn.ReadFromUDP(buffer)
		if err != nil {
			return
		}
		
		_, err = conn.WriteToUDP(buffer[:n], addr)
		if err != nil {
			return
		}
	}
}
