package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"runtime"
	"syscall"

	"tailscale-nat46/pkg/config"
	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/proxy"
)

var (
	appName    = "goproxy"
	appVersion = "1.0.0"
)

func main() {
	// Print banner
	printBanner()

	// Check if running on Windows
	if runtime.GOOS != "windows" {
		fmt.Fprintf(os.Stderr, "Error: %s only supports Windows\n", appName)
		os.Exit(1)
	}

	// Parse command line flags
	var (
		configFile = flag.String("config", "config.yaml", "Configuration file path")
		showHelp   = flag.Bool("help", false, "Show help information")
		showVer    = flag.Bool("version", false, "Show version information")
	)
	flag.Parse()

	if *showHelp {
		printHelp()
		return
	}

	if *showVer {
		fmt.Printf("%s version %s\n", appName, appVersion)
		return
	}

	// Load configuration
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error loading configuration: %v\n", err)
		os.Exit(1)
	}

	// Initialize logger
	log := logger.New(cfg.LogLevel, cfg.LogFile)
	log.Info("Starting %s v%s", appName, appVersion)
	log.Info("Configuration loaded from: %s", *configFile)

	// Create proxy instance
	p, err := proxy.NewProxy(cfg, log)
	if err != nil {
		log.Error("Failed to create proxy: %v", err)
		fmt.Fprintf(os.Stderr, "Failed to create proxy: %v\n", err)
		fmt.Fprintf(os.Stderr, "Common causes:\n")
		fmt.Fprintf(os.Stderr, "  - Not running as Administrator\n")
		fmt.Fprintf(os.Stderr, "  - Wintun driver not available\n")
		fmt.Fprintf(os.Stderr, "  - Invalid port configuration\n")
		fmt.Fprintf(os.Stderr, "  - Ports already in use\n")
		os.Exit(1)
	}

	// Set up signal handling for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start the proxy
	if err := p.Start(ctx); err != nil {
		log.Error("Failed to start proxy: %v", err)
		fmt.Fprintf(os.Stderr, "Failed to start proxy: %v\n", err)
		os.Exit(1)
	}

	log.Info("Proxy is running. Press Ctrl+C to stop.")

	// Wait for shutdown signal
	<-sigChan
	log.Info("Shutdown signal received, stopping proxy...")

	// Cancel context to signal shutdown
	cancel()

	// Stop the proxy
	if err := p.Stop(); err != nil {
		log.Error("Error stopping proxy: %v", err)
		os.Exit(1)
	}

	log.Info("Proxy stopped successfully")
}

func printBanner() {
	fmt.Printf(`
╔══════════════════════════════════════════════════════════════╗
║                      %s v%s                        ║
║                Port Forwarding Proxy                        ║
║              for Industrial Network Traffic                 ║
╚══════════════════════════════════════════════════════════════╝

`, appName, appVersion)
}

func printHelp() {
	fmt.Printf(`%s - Port Forwarding Proxy for Industrial Networks

USAGE:
    %s [OPTIONS]

OPTIONS:
    -config <file>    Configuration file path (default: config.yaml)
    -help             Show this help message
    -version          Show version information

EXAMPLES:
    %s                           # Use default config.yaml
    %s -config custom.yaml       # Use custom configuration file

REQUIREMENTS:
    - Windows 10/11 (64-bit)
    - Administrator privileges
    - Wintun driver (for virtual network interface)

DESCRIPTION:
    This tool creates a virtual network interface and forwards traffic
    from specified ports to the virtual interface IP (************).
    It's designed for industrial network environments where simple
    port forwarding is needed without protocol translation.

For more information, visit: https://github.com/your-repo/goproxy
`, appName, appName, appName, appName)
}
