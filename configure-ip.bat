@echo off
chcp 65001 >nul
title 配置 Wintun 接口 IP 地址

echo ========================================
echo Wintun 接口 IP 地址配置脚本
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限
    echo 请以管理员身份运行此脚本
    pause
    exit /b 1
)

echo ✅ 管理员权限检查通过
echo.

REM 设置默认值
set INTERFACE_NAME=tailscale-nat46
set IP_ADDRESS=************
set SUBNET_MASK=*************

REM 允许用户自定义
if "%1" neq "" set INTERFACE_NAME=%1
if "%2" neq "" set IP_ADDRESS=%2
if "%3" neq "" set SUBNET_MASK=%3

echo 配置参数:
echo   接口名称: %INTERFACE_NAME%
echo   IP 地址: %IP_ADDRESS%
echo   子网掩码: %SUBNET_MASK%
echo.

REM 检查接口是否存在
echo 1. 检查接口是否存在...
netsh interface show interface name="%INTERFACE_NAME%" >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 接口 "%INTERFACE_NAME%" 不存在
    echo 请先运行 tailscale-nat46.exe 或 test-wintun.exe 创建接口
    echo.
    echo 可用接口列表:
    netsh interface show interface
    pause
    exit /b 1
)
echo ✅ 接口 "%INTERFACE_NAME%" 存在

REM 配置 IP 地址
echo.
echo 2. 配置 IP 地址...
echo 执行命令: netsh interface ip set address "%INTERFACE_NAME%" static %IP_ADDRESS% %SUBNET_MASK%
netsh interface ip set address "%INTERFACE_NAME%" static %IP_ADDRESS% %SUBNET_MASK%
if %errorLevel% neq 0 (
    echo ❌ IP 地址配置失败
    pause
    exit /b 1
)
echo ✅ IP 地址配置成功

REM 启用接口
echo.
echo 3. 启用接口...
netsh interface set interface "%INTERFACE_NAME%" admin=enable
if %errorLevel% neq 0 (
    echo ❌ 接口启用失败
) else (
    echo ✅ 接口启用成功
)

REM 显示配置结果
echo.
echo 4. 验证配置...
echo.
echo 接口状态:
netsh interface show interface name="%INTERFACE_NAME%"
echo.
echo IP 配置:
netsh interface ip show address "%INTERFACE_NAME%"
echo.

REM 测试连通性
echo 5. 测试连通性...
echo 测试 ping %IP_ADDRESS%...
ping -n 1 %IP_ADDRESS% >nul 2>&1
if %errorLevel% equ 0 (
    echo ✅ Ping 测试成功
) else (
    echo ⚠️  Ping 测试失败（这可能是正常的）
)

echo.
echo ========================================
echo 配置完成
echo ========================================
echo.
echo 配置摘要:
echo   接口: %INTERFACE_NAME%
echo   IP: %IP_ADDRESS%
echo   掩码: %SUBNET_MASK%
echo.
echo 您现在可以:
echo   1. 检查配置: ipconfig /all
echo   2. 测试连接: ping %IP_ADDRESS%
echo   3. 查看路由: route print
echo.
echo 如需添加路由，请使用:
echo   route add 目标IP mask *************** %IP_ADDRESS% metric 1
echo.
echo 示例:
echo   route add ******** mask *************** %IP_ADDRESS% metric 1
echo.
pause
