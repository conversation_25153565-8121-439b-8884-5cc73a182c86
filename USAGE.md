# GoProxy 使用说明

## 概述

GoProxy 现在实现了基于 Wintun 数据包捕获的 IPv4 到 IPv6 直接转发功能。当客户端访问 10.1.1.x 地址时，程序会：

1. 通过 Wintun 虚拟网卡捕获数据包
2. 解析 IPv4 和 TCP 头部
3. 查找映射表，将 IPv4 地址映射到 IPv6 地址
4. 直接建立到 IPv6 服务器的连接
5. 进行双向数据转发
6. 构造响应包并通过 Wintun 发送回客户端

## 架构流程

```
[客户端访问 10.1.1.x:80] 
       ↓
[Wintun 捕获数据包]
       ↓
[解析 IPv4+TCP 头部] → [查找映射表] → [建立 IPv6 连接]
       ↓                              ↓
[构造响应包] ← [双向数据转发] ← [远端 IPv6 服务]
       ↓
[通过 Wintun 发送响应]
```

## 配置文件

编辑 `config.yaml` 配置 IPv4 到 IPv6 的映射：

```yaml
# 虚拟网卡 IP 地址
local_ipv4: "************"

# IPv4 到 IPv6 映射表
mappings:
  "********": "2001:db8::100"
  "********": "2001:db8::200"
  "********": "2001:db8::300"

# 其他配置
interface_name: "goproxy"
log_level: "info"
mtu: 1500
```

## 使用步骤

### 1. 构建程序
```bash
bash build.sh
```

### 2. 配置映射
编辑 `build/config.yaml`，添加您的 IPv4 到 IPv6 映射：

```yaml
mappings:
  "********": "您的IPv6地址1"
  "********": "您的IPv6地址2"
```

### 3. 运行程序
以管理员身份运行：
```cmd
goproxy.exe -config config.yaml
```

### 4. 测试连接
客户端程序现在可以访问 10.1.1.x 地址，流量会被自动转发到对应的 IPv6 地址。

## 工作原理

### 数据包捕获
- 使用 Wintun 虚拟网卡捕获所有发往 10.1.1.x 的数据包
- 解析 IPv4 和 TCP 头部信息
- 提取源/目标 IP、端口等信息

### 连接管理
- 为每个 TCP 连接维护状态
- 建立到 IPv6 服务器的直接连接
- 管理连接生命周期和清理

### 数据转发
- 客户端数据 → IPv6 服务器
- IPv6 服务器响应 → 客户端
- 维护 TCP 序列号和确认号
- 处理 SYN、ACK、FIN 等 TCP 标志

### 响应构造
- 构造正确的 TCP 响应包
- 设置正确的源/目标 IP 和端口
- 计算校验和
- 通过 Wintun 发送回客户端

## 支持的功能

- ✅ IPv4 到 IPv6 地址映射
- ✅ TCP 连接处理
- ✅ 数据包解析和构造
- ✅ 连接状态管理
- ✅ 双向数据转发
- ✅ 自动连接清理

## 限制

- 目前只支持 TCP 协议
- 只处理 10.1.1.x 网段的流量
- 需要管理员权限运行
- 仅支持 Windows 平台

## 故障排除

### 1. 权限问题
确保以管理员身份运行程序。

### 2. 虚拟网卡问题
检查 Wintun 驱动是否正确安装：
```cmd
netsh interface show interface
```

### 3. 连接问题
启用调试模式查看详细日志：
```yaml
log_level: "debug"
debug: true
```

### 4. IPv6 连接问题
确保目标 IPv6 地址可达：
```cmd
ping -6 2001:db8::100
```

## 测试

运行测试程序验证功能：
```cmd
test-proxy.exe
```

这将测试：
- 配置文件加载
- 端口可用性
- TCP/UDP 连接
- 基本网络功能

## 示例

假设您有一个运行在 `2001:db8::100:80` 的 IPv6 Web 服务器：

1. 配置映射：
```yaml
mappings:
  "********": "2001:db8::100"
```

2. 启动代理：
```cmd
goproxy.exe
```

3. 客户端访问：
```cmd
curl http://********/
```

流量将被自动转发到 `[2001:db8::100]:80`，响应会原路返回给客户端。

---

**注意**: 这是一个专为工业网络环境设计的解决方案，实现了从 IPv4 到 IPv6 的透明转发，无需修改客户端应用程序。
