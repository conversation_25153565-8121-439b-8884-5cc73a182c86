# Makefile for tailscale-nat46

# Variables
APP_NAME := tailscale-nat46
VERSION := 1.0.0
BUILD_DIR := build
DIST_DIR := dist

# Go build variables
GOOS := windows
GOARCH := amd64
CGO_ENABLED := 0

# Build flags
LDFLAGS := -s -w -X main.appVersion=$(VERSION)
BUILD_FLAGS := -ldflags="$(LDFLAGS)"

# Default target
.PHONY: all
all: clean build

# Build the application
.PHONY: build
build:
	@echo "Building $(APP_NAME) v$(VERSION) for $(GOOS)/$(GOARCH)..."
	@mkdir -p $(BUILD_DIR)
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME).exe ./cmd/$(APP_NAME)
	@cp examples/config.yaml $(BUILD_DIR)/
	@echo "Build complete: $(BUILD_DIR)/$(APP_NAME).exe"

# Build for multiple platforms
.PHONY: build-all
build-all: clean
	@echo "Building for multiple platforms..."
	@mkdir -p $(BUILD_DIR)
	
	# Windows AMD64
	GOOS=windows GOARCH=amd64 CGO_ENABLED=0 \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME)-windows-amd64.exe ./cmd/$(APP_NAME)
	
	# Windows ARM64
	GOOS=windows GOARCH=arm64 CGO_ENABLED=0 \
		go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(APP_NAME)-windows-arm64.exe ./cmd/$(APP_NAME)
	
	@echo "Multi-platform build complete"

# Create distribution package
.PHONY: dist
dist: build
	@echo "Creating distribution package..."
	@mkdir -p $(DIST_DIR)
	@cp $(BUILD_DIR)/$(APP_NAME).exe $(DIST_DIR)/
	@cp $(BUILD_DIR)/config.yaml $(DIST_DIR)/
	@cp README.md $(DIST_DIR)/
	@echo "Distribution package created in $(DIST_DIR)/"

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	go test -v ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage:
	@echo "Running tests with coverage..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Lint the code
.PHONY: lint
lint:
	@echo "Running linter..."
	golangci-lint run

# Format the code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	go fmt ./...

# Tidy dependencies
.PHONY: tidy
tidy:
	@echo "Tidying dependencies..."
	go mod tidy

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	@rm -rf $(BUILD_DIR)
	@rm -rf $(DIST_DIR)
	@rm -f coverage.out coverage.html

# Development build (with debug info)
.PHONY: dev
dev:
	@echo "Building development version..."
	@mkdir -p $(BUILD_DIR)
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
		go build -o $(BUILD_DIR)/$(APP_NAME)-dev.exe ./cmd/$(APP_NAME)
	@cp examples/config.yaml $(BUILD_DIR)/
	@echo "Development build complete: $(BUILD_DIR)/$(APP_NAME)-dev.exe"

# Build test programs
.PHONY: build-tests
build-tests:
	@echo "Building test programs..."
	@mkdir -p $(BUILD_DIR)
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
		go build -o $(BUILD_DIR)/test-translation.exe ./cmd/test-translation
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
		go build -o $(BUILD_DIR)/test-wintun.exe ./cmd/test-wintun
	GOOS=$(GOOS) GOARCH=$(GOARCH) CGO_ENABLED=$(CGO_ENABLED) \
		go build -o $(BUILD_DIR)/test-routing.exe ./cmd/test-routing
	@echo "Test programs built successfully"

# Install dependencies
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	go mod download

# Verify the build
.PHONY: verify
verify: build
	@echo "Verifying build..."
	@if [ -f "$(BUILD_DIR)/$(APP_NAME).exe" ]; then \
		echo "✓ Executable exists"; \
		file $(BUILD_DIR)/$(APP_NAME).exe; \
	else \
		echo "✗ Executable not found"; \
		exit 1; \
	fi

# Help
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean and build (default)"
	@echo "  build        - Build the application"
	@echo "  build-all    - Build for multiple platforms"
	@echo "  build-tests  - Build test programs"
	@echo "  dist         - Create distribution package"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage"
	@echo "  lint         - Run linter"
	@echo "  fmt          - Format code"
	@echo "  tidy         - Tidy dependencies"
	@echo "  clean        - Clean build artifacts"
	@echo "  dev          - Development build"
	@echo "  deps         - Install dependencies"
	@echo "  verify       - Verify build"
	@echo "  help         - Show this help"
