package stats

import (
	"sync/atomic"
	"time"
)

// Stats represents runtime statistics
type Stats struct {
	// Packet counters
	PacketsReceived   uint64
	PacketsSent       uint64
	PacketsDropped    uint64
	PacketsTranslated uint64

	// Byte counters
	BytesReceived   uint64
	BytesSent       uint64
	BytesTranslated uint64

	// Connection counters
	ConnectionsActive uint64
	ConnectionsTotal  uint64

	// Error counters
	ErrorsTotal      uint64
	ErrorsTranslation uint64
	ErrorsNetwork     uint64

	// Timing
	StartTime time.Time
	LastReset time.Time
}

// New creates a new stats instance
func New() *Stats {
	now := time.Now()
	return &Stats{
		StartTime: now,
		LastReset: now,
	}
}

// IncrementPacketsReceived increments the packets received counter
func (s *Stats) IncrementPacketsReceived() {
	atomic.AddUint64(&s.PacketsReceived, 1)
}

// IncrementPacketsSent increments the packets sent counter
func (s *Stats) IncrementPacketsSent() {
	atomic.AddUint64(&s.Packets<PERSON>ent, 1)
}

// IncrementPacketsDropped increments the packets dropped counter
func (s *Stats) IncrementPacketsDropped() {
	atomic.AddUint64(&s.PacketsDropped, 1)
}

// IncrementPacketsTranslated increments the packets translated counter
func (s *Stats) IncrementPacketsTranslated() {
	atomic.AddUint64(&s.PacketsTranslated, 1)
}

// AddBytesReceived adds to the bytes received counter
func (s *Stats) AddBytesReceived(bytes uint64) {
	atomic.AddUint64(&s.BytesReceived, bytes)
}

// AddBytesSent adds to the bytes sent counter
func (s *Stats) AddBytesSent(bytes uint64) {
	atomic.AddUint64(&s.BytesSent, bytes)
}

// AddBytesTranslated adds to the bytes translated counter
func (s *Stats) AddBytesTranslated(bytes uint64) {
	atomic.AddUint64(&s.BytesTranslated, bytes)
}

// IncrementConnectionsActive increments the active connections counter
func (s *Stats) IncrementConnectionsActive() {
	atomic.AddUint64(&s.ConnectionsActive, 1)
	atomic.AddUint64(&s.ConnectionsTotal, 1)
}

// DecrementConnectionsActive decrements the active connections counter
func (s *Stats) DecrementConnectionsActive() {
	atomic.AddUint64(&s.ConnectionsActive, ^uint64(0)) // Subtract 1
}

// IncrementErrorsTotal increments the total errors counter
func (s *Stats) IncrementErrorsTotal() {
	atomic.AddUint64(&s.ErrorsTotal, 1)
}

// IncrementErrorsTranslation increments the translation errors counter
func (s *Stats) IncrementErrorsTranslation() {
	atomic.AddUint64(&s.ErrorsTranslation, 1)
	s.IncrementErrorsTotal()
}

// IncrementErrorsNetwork increments the network errors counter
func (s *Stats) IncrementErrorsNetwork() {
	atomic.AddUint64(&s.ErrorsNetwork, 1)
	s.IncrementErrorsTotal()
}

// Reset resets all counters
func (s *Stats) Reset() {
	atomic.StoreUint64(&s.PacketsReceived, 0)
	atomic.StoreUint64(&s.PacketsSent, 0)
	atomic.StoreUint64(&s.PacketsDropped, 0)
	atomic.StoreUint64(&s.PacketsTranslated, 0)
	atomic.StoreUint64(&s.BytesReceived, 0)
	atomic.StoreUint64(&s.BytesSent, 0)
	atomic.StoreUint64(&s.BytesTranslated, 0)
	atomic.StoreUint64(&s.ConnectionsActive, 0)
	atomic.StoreUint64(&s.ConnectionsTotal, 0)
	atomic.StoreUint64(&s.ErrorsTotal, 0)
	atomic.StoreUint64(&s.ErrorsTranslation, 0)
	atomic.StoreUint64(&s.ErrorsNetwork, 0)
	s.LastReset = time.Now()
}

// GetUptime returns the uptime since start
func (s *Stats) GetUptime() time.Duration {
	return time.Since(s.StartTime)
}

// GetTimeSinceReset returns the time since last reset
func (s *Stats) GetTimeSinceReset() time.Duration {
	return time.Since(s.LastReset)
}
