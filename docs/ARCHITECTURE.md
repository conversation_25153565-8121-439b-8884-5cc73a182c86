# tailscale-nat46 Architecture

## Overview

tailscale-nat46 is a Windows-specific IPv4-to-IPv6 transparent proxy designed to work seamlessly with Tailscale networks. It enables legacy IPv4-only applications to communicate with IPv6-only devices in your Tailscale network through transparent packet translation.

## Core Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   IPv4 App      │    │  tailscale-nat46 │    │  IPv6 Device    │
│                 │    │                  │    │  (Tailscale)    │
│ connects to     │───▶│  Wintun Virtual  │───▶│                 │
│ ********:80     │    │  Interface       │    │ [fd7a:...]:80   │
│                 │◀───│                  │◀───│                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Components

### 1. Wintun Virtual Network Interface (`pkg/wintun/`)

**Purpose**: Creates and manages a virtual network interface using the Wintun driver.

**Responsibilities**:
- Create a virtual network interface named "tailscale-nat46"
- Configure the interface with a local IPv4 address (e.g., ************)
- Read IPv4 packets from the interface
- Write IPv4 packets to the interface

**Key Features**:
- Uses the same Wintun driver as Tailscale (no driver conflicts)
- High-performance packet I/O
- Proper cleanup on shutdown

### 2. Routing Management (`pkg/routing/`)

**Purpose**: Manages Windows routing table to intercept specific IPv4 traffic.

**Responsibilities**:
- Add precise routing rules for mapped IPv4 addresses
- Ensure routes point to the virtual interface
- Avoid conflicts with existing Tailscale routes (**********/10)
- Clean up routes on shutdown

**Example Route**:
```
route add ******** mask *************** ************ metric 1
```

### 3. User-Space Network Stack (`pkg/netstack/`)

**Purpose**: Parse and construct IPv4/IPv6 packets in user space.

**Responsibilities**:
- Parse incoming IPv4 packets (extract headers, payload)
- Construct outgoing IPv6 packets
- Parse incoming IPv6 packets (for return traffic)
- Construct outgoing IPv4 packets (for return traffic)
- Handle TCP, UDP, and ICMP protocols

**Implementation**: Based on gVisor's netstack or similar user-space network stack.

### 4. IPv4-to-IPv6 Translation Engine (`pkg/translator/`)

**Purpose**: Core packet translation logic.

**Responsibilities**:
- Map IPv4 addresses to IPv6 addresses based on configuration
- Translate IPv4 packet headers to IPv6
- Preserve transport layer information (TCP/UDP ports, sequence numbers)
- Handle protocol-specific translations (ICMP ↔ ICMPv6)
- Maintain connection state for bidirectional translation

### 5. Raw Socket Communication (`pkg/rawsocket/`)

**Purpose**: Inject IPv6 packets into the physical network interface.

**Responsibilities**:
- Create raw sockets for IPv6 communication
- Send translated IPv6 packets to the physical network
- Capture IPv6 response packets from Tailscale
- Filter packets to only process relevant traffic

### 6. Configuration Management (`pkg/config/`)

**Purpose**: Handle application configuration.

**Features**:
- YAML configuration file support
- Command-line argument parsing
- IP mapping table management
- Runtime configuration validation

### 7. Logging and Statistics (`pkg/logger/`, `internal/stats/`)

**Purpose**: Provide observability and debugging capabilities.

**Features**:
- Structured logging with multiple levels
- Real-time statistics (packets, bytes, connections, errors)
- Performance metrics
- Debug information for troubleshooting

## Data Flow

### Outbound Traffic (IPv4 → IPv6)

1. **Application** sends IPv4 packet to ********
2. **Windows routing** directs packet to virtual interface (************)
3. **Wintun interface** receives the packet
4. **Netstack** parses the IPv4 packet
5. **Translator** maps ******** → fd7a:115c:a1e0:b1a:0:7:c0a8:601
6. **Translator** constructs IPv6 packet with:
   - Source: Local Tailscale IPv6 address
   - Destination: Mapped IPv6 address
   - Payload: Original IPv4 payload
7. **Raw socket** injects IPv6 packet into physical interface
8. **Tailscale** encrypts and routes the packet

### Inbound Traffic (IPv6 → IPv4)

1. **Tailscale** receives encrypted IPv6 packet
2. **Tailscale** decrypts and delivers to local interface
3. **Raw socket** captures the IPv6 packet
4. **Netstack** parses the IPv6 packet
5. **Translator** maps fd7a:...601 → ********
6. **Translator** constructs IPv4 packet with:
   - Source: ********
   - Destination: Application's address
   - Payload: Original IPv6 payload
7. **Wintun interface** sends IPv4 packet
8. **Windows routing** delivers to application

## Security Considerations

### Privilege Requirements
- **Administrator privileges** required for:
  - Creating virtual network interfaces
  - Modifying routing tables
  - Creating raw sockets

### Network Isolation
- Only processes specific IPv4 addresses (configured mappings)
- Does not interfere with other network traffic
- Maintains Tailscale's security model

### Error Handling
- Graceful degradation on component failures
- Proper cleanup of system resources
- Detailed error logging for troubleshooting

## Performance Characteristics

### Throughput
- User-space packet processing (no kernel transitions for translation)
- Efficient memory management with packet pools
- Parallel processing with configurable worker threads

### Latency
- Minimal additional latency (typically < 1ms)
- Direct memory copying for packet data
- Optimized packet parsing and construction

### Resource Usage
- Low memory footprint (< 50MB typical)
- Minimal CPU usage when idle
- Scales with network traffic volume

## Deployment Model

### Single Executable
- Self-contained Windows executable
- Embedded Wintun library (or requires wintun.dll)
- Configuration via YAML file

### Installation
1. Copy executable to target machine
2. Create configuration file with IP mappings
3. Run as Administrator
4. Tool automatically configures network interfaces and routing

### Compatibility
- Windows 10/11 (64-bit)
- Compatible with existing Tailscale installation
- No conflicts with other VPN software (when properly configured)
