//go:build windows

package routing

import (
	"fmt"
	"net"
	"os/exec"
	"strings"
	"unsafe"

	"golang.org/x/sys/windows"
)

// GetInterfaceIndexByIP gets the interface index for a given IP address
func GetInterfaceIndexByIP(ip net.IP) (uint32, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return 0, fmt.Errorf("failed to get interfaces: %w", err)
	}

	for _, iface := range interfaces {
		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}

		for _, addr := range addrs {
			var ifaceIP net.IP
			switch v := addr.(type) {
			case *net.IPNet:
				ifaceIP = v.IP
			case *net.IPAddr:
				ifaceIP = v.IP
			}

			if ifaceIP != nil && ifaceIP.Equal(ip) {
				return uint32(iface.Index), nil
			}
		}
	}

	return 0, fmt.Errorf("interface not found for IP %s", ip.String())
}

// GetInterfaceIndexByName gets the interface index for a given interface name
func GetInterfaceIndexByName(name string) (uint32, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return 0, fmt.Errorf("failed to get interfaces: %w", err)
	}

	for _, iface := range interfaces {
		if iface.Name == name {
			return uint32(iface.Index), nil
		}
	}

	return 0, fmt.Errorf("interface not found: %s", name)
}

// GetDefaultGateway gets the default gateway IP address
func GetDefaultGateway() (net.IP, error) {
	// Use route print command to get default gateway
	cmd := exec.Command("route", "print", "0.0.0.0")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to run route command: %w", err)
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		fields := strings.Fields(line)
		if len(fields) >= 3 && fields[0] == "0.0.0.0" && fields[1] == "0.0.0.0" {
			gateway := net.ParseIP(fields[2])
			if gateway != nil {
				return gateway, nil
			}
		}
	}

	return nil, fmt.Errorf("default gateway not found")
}

// CheckRouteExists checks if a route exists in the routing table
func CheckRouteExists(destination net.IP, mask net.IPMask) (bool, error) {
	dest4 := destination.To4()
	if dest4 == nil {
		return false, fmt.Errorf("only IPv4 routes are supported")
	}

	// Create route entry for lookup
	var row MIB_IPFORWARD_ROW2

	// Set destination
	destSockaddr := (*windows.RawSockaddrInet4)(unsafe.Pointer(&row.DestinationPrefix))
	destSockaddr.Family = windows.AF_INET
	copy(destSockaddr.Addr[:], dest4)

	// Calculate prefix length from mask
	prefixLen, _ := mask.Size()
	row.SitePrefixLength = uint8(prefixLen)

	// Call Windows API to check if route exists
	ret, _, _ := procGetIpForwardEntry2.Call(uintptr(unsafe.Pointer(&row)))
	return ret == NO_ERROR, nil
}

// ValidateRoute validates route parameters
func ValidateRoute(destination net.IP, mask net.IPMask, gateway net.IP, interfaceIndex uint32) error {
	if destination == nil {
		return fmt.Errorf("destination cannot be nil")
	}

	if gateway == nil {
		return fmt.Errorf("gateway cannot be nil")
	}

	dest4 := destination.To4()
	if dest4 == nil {
		return fmt.Errorf("destination must be IPv4")
	}

	gw4 := gateway.To4()
	if gw4 == nil {
		return fmt.Errorf("gateway must be IPv4")
	}

	if mask == nil {
		return fmt.Errorf("mask cannot be nil")
	}

	// Validate mask
	ones, bits := mask.Size()
	if bits != 32 {
		return fmt.Errorf("mask must be for IPv4 (32 bits)")
	}

	if ones < 0 || ones > 32 {
		return fmt.Errorf("invalid mask prefix length: %d", ones)
	}

	if interfaceIndex == 0 {
		return fmt.Errorf("interface index cannot be 0")
	}

	return nil
}

// FormatRoute formats a route for display
func FormatRoute(route *Route) string {
	if route == nil {
		return ""
	}

	ones, _ := route.Mask.Size()
	return fmt.Sprintf("%s/%d via %s (if:%d, metric:%d, added:%t)",
		route.Destination.String(),
		ones,
		route.Gateway.String(),
		route.Interface,
		route.Metric,
		route.Added,
	)
}

// ParseCIDR parses a CIDR notation string into IP and mask
func ParseCIDR(cidr string) (net.IP, net.IPMask, error) {
	ip, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return nil, nil, fmt.Errorf("invalid CIDR: %w", err)
	}

	return ip, ipnet.Mask, nil
}

// IPToUint32 converts an IPv4 address to uint32
func IPToUint32(ip net.IP) uint32 {
	ip4 := ip.To4()
	if ip4 == nil {
		return 0
	}

	return uint32(ip4[0])<<24 | uint32(ip4[1])<<16 | uint32(ip4[2])<<8 | uint32(ip4[3])
}

// Uint32ToIP converts a uint32 to IPv4 address
func Uint32ToIP(n uint32) net.IP {
	return net.IPv4(byte(n>>24), byte(n>>16), byte(n>>8), byte(n))
}

// GetMetricForInterface gets the metric for a specific interface
func GetMetricForInterface(interfaceIndex uint32) (uint32, error) {
	// Default metric if we can't determine it
	defaultMetric := uint32(1)

	// Try to get interface information
	interfaces, err := net.Interfaces()
	if err != nil {
		return defaultMetric, nil
	}

	for _, iface := range interfaces {
		if uint32(iface.Index) == interfaceIndex {
			// For virtual interfaces, use a low metric
			if strings.Contains(strings.ToLower(iface.Name), "wintun") ||
				strings.Contains(strings.ToLower(iface.Name), "tun") ||
				strings.Contains(strings.ToLower(iface.Name), "tap") {
				return 1, nil
			}
			// For physical interfaces, use a higher metric
			return 10, nil
		}
	}

	return defaultMetric, nil
}

// IsPrivateIP checks if an IP address is in a private range
func IsPrivateIP(ip net.IP) bool {
	if ip == nil {
		return false
	}

	ip4 := ip.To4()
	if ip4 == nil {
		return false
	}

	// Check private ranges
	// 10.0.0.0/8
	if ip4[0] == 10 {
		return true
	}

	// **********/12
	if ip4[0] == 172 && ip4[1] >= 16 && ip4[1] <= 31 {
		return true
	}

	// ***********/16
	if ip4[0] == 192 && ip4[1] == 168 {
		return true
	}

	return false
}

// IsTailscaleIP checks if an IP address is in the Tailscale range
func IsTailscaleIP(ip net.IP) bool {
	if ip == nil {
		return false
	}

	ip4 := ip.To4()
	if ip4 == nil {
		return false
	}

	// Tailscale uses **********/10
	if ip4[0] == 100 && (ip4[1]&0xC0) == 64 {
		return true
	}

	return false
}

// GetBestMetric calculates the best metric for a route
func GetBestMetric(destination net.IP, interfaceIndex uint32) uint32 {
	// Host routes get priority
	if destination != nil {
		// For host routes (/32), use metric 1
		return 1
	}

	// Get interface-specific metric
	metric, _ := GetMetricForInterface(interfaceIndex)
	return metric
}

// ConvertLUIDToIndex converts interface LUID to index
func ConvertLUIDToIndex(luid uint64) (uint32, error) {
	// This would typically use ConvertInterfaceLuidToIndex Windows API
	// For now, we'll return an error indicating it's not implemented
	return 0, fmt.Errorf("LUID to index conversion not implemented")
}

// ConvertIndexToLUID converts interface index to LUID
func ConvertIndexToLUID(index uint32) (uint64, error) {
	// This would typically use ConvertInterfaceIndexToLuid Windows API
	// For now, we'll return an error indicating it's not implemented
	return 0, fmt.Errorf("index to LUID conversion not implemented")
}
