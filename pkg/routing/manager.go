//go:build windows

package routing

import (
	"fmt"
	"net"
	"os/exec"
	"strings"
	"sync"
	"unsafe"

	"golang.org/x/sys/windows"

	"tailscale-nat46/pkg/logger"
)

// Manager manages Windows routing table entries
type Manager struct {
	logger *logger.Logger
	routes map[string]*Route // Track managed routes
	mu     sync.RWMutex
}

// Route represents a routing table entry
type Route struct {
	Destination net.IP
	Mask        net.IPMask
	Gateway     net.IP
	Interface   uint32
	Metric      uint32
	Added       bool // Track if we added this route
}

// Windows API constants
const (
	NO_ERROR                    = 0
	ERROR_INVALID_PARAMETER     = 87
	ERROR_OBJECT_ALREADY_EXISTS = 5010
	ERROR_NOT_FOUND             = 1168
)

// MIB_IPFORWARD_ROW2 structure for Windows routing API
type MIB_IPFORWARD_ROW2 struct {
	InterfaceLuid        uint64
	InterfaceIndex       uint32
	DestinationPrefix    windows.RawSockaddrInet4
	NextHop              windows.RawSockaddrInet4
	SitePrefixLength     uint8
	ValidLifetime        uint32
	PreferredLifetime    uint32
	Metric               uint32
	Protocol             uint32
	Loopback             uint8
	AutoconfigureAddress uint8
	Publish              uint8
	Immortal             uint8
	Age                  uint32
	Origin               uint32
}

// Windows API functions
var (
	iphlpapi = windows.NewLazySystemDLL("iphlpapi.dll")

	procCreateIpForwardEntry2 = iphlpapi.NewProc("CreateIpForwardEntry2")
	procDeleteIpForwardEntry2 = iphlpapi.NewProc("DeleteIpForwardEntry2")
	procGetIpForwardEntry2    = iphlpapi.NewProc("GetIpForwardEntry2")
)

// NewManager creates a new routing manager
func NewManager(logger *logger.Logger) *Manager {
	if logger == nil {
		panic("logger cannot be nil")
	}

	return &Manager{
		logger: logger,
		routes: make(map[string]*Route),
	}
}

// AddRoute adds a route to the routing table
func (m *Manager) AddRoute(destination net.IP, mask net.IPMask, gateway net.IP, interfaceIndex uint32, metric uint32) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if destination == nil || gateway == nil {
		return fmt.Errorf("destination and gateway cannot be nil")
	}

	// Convert to IPv4 if needed
	dest4 := destination.To4()
	gw4 := gateway.To4()
	if dest4 == nil || gw4 == nil {
		return fmt.Errorf("only IPv4 routes are supported")
	}

	routeKey := fmt.Sprintf("%s/%s", dest4.String(), net.IP(mask).String())

	m.logger.Info("Adding route: %s via %s (interface %d, metric %d)",
		routeKey, gw4.String(), interfaceIndex, metric)

	// Check if route already exists
	if route, exists := m.routes[routeKey]; exists && route.Added {
		m.logger.Debug("Route already exists: %s", routeKey)
		return nil
	}

	// Try Windows API first, then fallback to route command
	err := m.addRouteAPI(dest4, mask, gw4, interfaceIndex, metric)
	if err != nil {
		m.logger.Warn("Windows API failed: %v, trying route command", err)
		err = m.addRouteCommand(dest4, mask, gw4, metric)
		if err != nil {
			return fmt.Errorf("both API and command failed: %v", err)
		}
	}

	// Store the route for cleanup
	m.routes[routeKey] = &Route{
		Destination: dest4,
		Mask:        mask,
		Gateway:     gw4,
		Interface:   interfaceIndex,
		Metric:      metric,
		Added:       true,
	}

	m.logger.Info("Successfully added route: %s", routeKey)
	return nil
}

// addRouteAPI adds route using Windows API
func (m *Manager) addRouteAPI(dest4 net.IP, mask net.IPMask, gw4 net.IP, interfaceIndex uint32, metric uint32) error {
	m.logger.Debug("Attempting to add route via Windows API: %s -> %s (interface %d)",
		dest4.String(), gw4.String(), interfaceIndex)

	// Create the route entry
	var row MIB_IPFORWARD_ROW2
	row.InterfaceIndex = interfaceIndex
	row.Metric = metric
	row.ValidLifetime = 0xffffffff
	row.PreferredLifetime = 0xffffffff
	row.Protocol = 3 // MIB_IPPROTO_NETMGMT

	// Set destination
	destSockaddr := (*windows.RawSockaddrInet4)(unsafe.Pointer(&row.DestinationPrefix))
	destSockaddr.Family = windows.AF_INET
	copy(destSockaddr.Addr[:], dest4)

	// Set gateway
	gwSockaddr := (*windows.RawSockaddrInet4)(unsafe.Pointer(&row.NextHop))
	gwSockaddr.Family = windows.AF_INET
	copy(gwSockaddr.Addr[:], gw4)

	// Calculate prefix length from mask
	prefixLen, _ := mask.Size()
	row.SitePrefixLength = uint8(prefixLen)

	m.logger.Debug("Route entry: dest=%s, gw=%s, interface=%d, metric=%d, prefix=%d",
		dest4.String(), gw4.String(), interfaceIndex, metric, prefixLen)

	// Call Windows API
	ret, _, err := procCreateIpForwardEntry2.Call(uintptr(unsafe.Pointer(&row)))
	if ret != NO_ERROR {
		if ret == ERROR_OBJECT_ALREADY_EXISTS {
			m.logger.Debug("Route already exists in system")
			return nil
		}
		return fmt.Errorf("CreateIpForwardEntry2 failed: %v (code: %d)", err, ret)
	}

	m.logger.Debug("Windows API route addition successful")
	return nil
}

// addRouteCommand adds route using route command
func (m *Manager) addRouteCommand(dest4 net.IP, mask net.IPMask, gw4 net.IP, metric uint32) error {
	// Convert mask to string
	maskStr := net.IP(mask).String()

	// Build route command arguments
	args := []string{"add", dest4.String(), "mask", maskStr, gw4.String(), "metric", fmt.Sprintf("%d", metric)}

	m.logger.Debug("Executing: route %v", args)

	// Execute the route command
	cmd := exec.Command("route", args...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		// Check if it's because route already exists
		if strings.Contains(string(output), "already exists") ||
			strings.Contains(string(output), "duplicate") {
			m.logger.Debug("Route already exists (from command output)")
			return nil
		}
		return fmt.Errorf("route command failed: %v, output: %s", err, string(output))
	}

	m.logger.Debug("Route command output: %s", string(output))
	m.logger.Info("Route added successfully via command line")
	return nil
}

// RemoveRoute removes a route from the routing table
func (m *Manager) RemoveRoute(destination net.IP, mask net.IPMask) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if destination == nil {
		return fmt.Errorf("destination cannot be nil")
	}

	dest4 := destination.To4()
	if dest4 == nil {
		return fmt.Errorf("only IPv4 routes are supported")
	}

	routeKey := fmt.Sprintf("%s/%s", dest4.String(), net.IP(mask).String())

	route, exists := m.routes[routeKey]
	if !exists {
		m.logger.Debug("Route not found in managed routes: %s", routeKey)
		return nil
	}

	if !route.Added {
		m.logger.Debug("Route was not added by us, not removing: %s", routeKey)
		delete(m.routes, routeKey)
		return nil
	}

	m.logger.Info("Removing route: %s", routeKey)

	// Create the route entry for deletion
	var row MIB_IPFORWARD_ROW2
	row.InterfaceIndex = route.Interface

	// Set destination
	destSockaddr := (*windows.RawSockaddrInet4)(unsafe.Pointer(&row.DestinationPrefix))
	destSockaddr.Family = windows.AF_INET
	copy(destSockaddr.Addr[:], dest4)

	// Set gateway
	gwSockaddr := (*windows.RawSockaddrInet4)(unsafe.Pointer(&row.NextHop))
	gwSockaddr.Family = windows.AF_INET
	copy(gwSockaddr.Addr[:], route.Gateway.To4())

	// Calculate prefix length from mask
	prefixLen, _ := mask.Size()
	row.SitePrefixLength = uint8(prefixLen)

	// Call Windows API
	ret, _, err := procDeleteIpForwardEntry2.Call(uintptr(unsafe.Pointer(&row)))
	if ret != NO_ERROR {
		if ret == ERROR_NOT_FOUND {
			m.logger.Debug("Route not found in system (already removed): %s", routeKey)
		} else {
			m.logger.Error("DeleteIpForwardEntry2 failed: %v (code: %d)", err, ret)
			return fmt.Errorf("DeleteIpForwardEntry2 failed: %v (code: %d)", err, ret)
		}
	} else {
		m.logger.Info("Successfully removed route: %s", routeKey)
	}

	// Remove from our tracking
	delete(m.routes, routeKey)
	return nil
}

// AddHostRoute adds a host route (/32) for a specific IP
func (m *Manager) AddHostRoute(destination net.IP, gateway net.IP, interfaceIndex uint32, metric uint32) error {
	mask := net.CIDRMask(32, 32) // /32 mask for host route
	return m.AddRoute(destination, mask, gateway, interfaceIndex, metric)
}

// RemoveHostRoute removes a host route (/32) for a specific IP
func (m *Manager) RemoveHostRoute(destination net.IP) error {
	mask := net.CIDRMask(32, 32) // /32 mask for host route
	return m.RemoveRoute(destination, mask)
}

// RemoveAllRoutes removes all routes managed by this manager
func (m *Manager) RemoveAllRoutes() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.logger.Info("Removing all managed routes (%d routes)", len(m.routes))

	var errors []error
	for routeKey, route := range m.routes {
		if route.Added {
			if err := m.removeRouteUnsafe(route.Destination, route.Mask); err != nil {
				m.logger.Error("Failed to remove route %s: %v", routeKey, err)
				errors = append(errors, err)
			}
		}
	}

	// Clear all routes
	m.routes = make(map[string]*Route)

	if len(errors) > 0 {
		return fmt.Errorf("failed to remove %d routes", len(errors))
	}

	m.logger.Info("All managed routes removed successfully")
	return nil
}

// removeRouteUnsafe removes a route without locking (internal use)
func (m *Manager) removeRouteUnsafe(destination net.IP, mask net.IPMask) error {
	dest4 := destination.To4()
	if dest4 == nil {
		return fmt.Errorf("only IPv4 routes are supported")
	}

	routeKey := fmt.Sprintf("%s/%s", dest4.String(), net.IP(mask).String())
	route, exists := m.routes[routeKey]
	if !exists || !route.Added {
		return nil
	}

	// Create the route entry for deletion
	var row MIB_IPFORWARD_ROW2
	row.InterfaceIndex = route.Interface

	// Set destination
	destSockaddr := (*windows.RawSockaddrInet4)(unsafe.Pointer(&row.DestinationPrefix))
	destSockaddr.Family = windows.AF_INET
	copy(destSockaddr.Addr[:], dest4)

	// Set gateway
	gwSockaddr := (*windows.RawSockaddrInet4)(unsafe.Pointer(&row.NextHop))
	gwSockaddr.Family = windows.AF_INET
	copy(gwSockaddr.Addr[:], route.Gateway.To4())

	// Calculate prefix length from mask
	prefixLen, _ := mask.Size()
	row.SitePrefixLength = uint8(prefixLen)

	// Call Windows API
	ret, _, err := procDeleteIpForwardEntry2.Call(uintptr(unsafe.Pointer(&row)))
	if ret != NO_ERROR && ret != ERROR_NOT_FOUND {
		return fmt.Errorf("DeleteIpForwardEntry2 failed: %v (code: %d)", err, ret)
	}

	return nil
}

// GetManagedRoutes returns a copy of all managed routes
func (m *Manager) GetManagedRoutes() map[string]*Route {
	m.mu.RLock()
	defer m.mu.RUnlock()

	routes := make(map[string]*Route)
	for key, route := range m.routes {
		routes[key] = &Route{
			Destination: route.Destination,
			Mask:        route.Mask,
			Gateway:     route.Gateway,
			Interface:   route.Interface,
			Metric:      route.Metric,
			Added:       route.Added,
		}
	}

	return routes
}

// GetRouteCount returns the number of managed routes
func (m *Manager) GetRouteCount() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return len(m.routes)
}
