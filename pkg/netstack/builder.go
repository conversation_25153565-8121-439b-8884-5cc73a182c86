package netstack

import (
	"encoding/binary"
	"net"
)

// PacketBuilder provides functionality to build network packets
type PacketBuilder struct{}

// NewPacketBuilder creates a new packet builder
func NewPacketBuilder() *PacketBuilder {
	return &PacketBuilder{}
}

// BuildIPv4Packet builds an IPv4 packet
func (pb *PacketBuilder) BuildIPv4Packet(header *IPv4Header, payload []byte) []byte {
	// Calculate total length
	headerLen := 20 // Basic IPv4 header length
	totalLen := headerLen + len(payload)
	header.TotalLength = uint16(totalLen)

	// Build packet
	packet := make([]byte, totalLen)

	// IPv4 header
	packet[0] = (header.Version << 4) | 5 // Version (4) + IHL (5 = 20 bytes)
	packet[1] = header.TypeOfService
	binary.BigEndian.PutUint16(packet[2:4], header.TotalLength)
	binary.BigEndian.PutUint16(packet[4:6], header.Identification)
	binary.BigEndian.PutUint16(packet[6:8], header.Flags)
	packet[8] = header.TTL
	packet[9] = header.Protocol
	// Checksum will be calculated later
	copy(packet[12:16], header.SrcIP.To4())
	copy(packet[16:20], header.DstIP.To4())

	// Calculate and set checksum
	header.Checksum = pb.calculateIPv4Checksum(packet[:headerLen])
	binary.BigEndian.PutUint16(packet[10:12], header.Checksum)

	// Copy payload
	copy(packet[headerLen:], payload)

	return packet
}

// BuildIPv6Packet builds an IPv6 packet
func (pb *PacketBuilder) BuildIPv6Packet(header *IPv6Header, payload []byte) []byte {
	// Calculate payload length
	header.PayloadLen = uint16(len(payload))

	// Build packet
	headerLen := 40 // IPv6 header length
	totalLen := headerLen + len(payload)
	packet := make([]byte, totalLen)

	// IPv6 header
	packet[0] = (header.Version << 4) | (header.TrafficClass >> 4)
	packet[1] = (header.TrafficClass << 4) | uint8((header.FlowLabel >> 16) & 0x0F)
	packet[2] = uint8((header.FlowLabel >> 8) & 0xFF)
	packet[3] = uint8(header.FlowLabel & 0xFF)
	binary.BigEndian.PutUint16(packet[4:6], header.PayloadLen)
	packet[6] = header.NextHeader
	packet[7] = header.HopLimit
	copy(packet[8:24], header.SrcIP.To16())
	copy(packet[24:40], header.DstIP.To16())

	// Copy payload
	copy(packet[headerLen:], payload)

	return packet
}

// BuildTCPSegment builds a TCP segment
func (pb *PacketBuilder) BuildTCPSegment(header *TCPHeader, payload []byte) []byte {
	// Calculate total length
	headerLen := 20 // Basic TCP header length
	totalLen := headerLen + len(payload)

	// Build segment
	segment := make([]byte, totalLen)

	// TCP header
	binary.BigEndian.PutUint16(segment[0:2], header.SrcPort)
	binary.BigEndian.PutUint16(segment[2:4], header.DstPort)
	binary.BigEndian.PutUint32(segment[4:8], header.SeqNum)
	binary.BigEndian.PutUint32(segment[8:12], header.AckNum)
	segment[12] = (5 << 4) // Data offset (5 = 20 bytes) + reserved
	segment[13] = header.Flags
	binary.BigEndian.PutUint16(segment[14:16], header.Window)
	// Checksum will be calculated by caller if needed
	binary.BigEndian.PutUint16(segment[16:18], header.Checksum)
	binary.BigEndian.PutUint16(segment[18:20], header.UrgentPtr)

	// Copy payload
	copy(segment[headerLen:], payload)

	return segment
}

// BuildUDPDatagram builds a UDP datagram
func (pb *PacketBuilder) BuildUDPDatagram(header *UDPHeader, payload []byte) []byte {
	// Calculate length
	headerLen := 8 // UDP header length
	totalLen := headerLen + len(payload)
	header.Length = uint16(totalLen)

	// Build datagram
	datagram := make([]byte, totalLen)

	// UDP header
	binary.BigEndian.PutUint16(datagram[0:2], header.SrcPort)
	binary.BigEndian.PutUint16(datagram[2:4], header.DstPort)
	binary.BigEndian.PutUint16(datagram[4:6], header.Length)
	// Checksum will be calculated by caller if needed
	binary.BigEndian.PutUint16(datagram[6:8], header.Checksum)

	// Copy payload
	copy(datagram[headerLen:], payload)

	return datagram
}

// BuildICMPMessage builds an ICMP message
func (pb *PacketBuilder) BuildICMPMessage(header *ICMPHeader, payload []byte) []byte {
	// Calculate total length
	headerLen := 8 // Basic ICMP header length
	totalLen := headerLen + len(payload)

	// Build message
	message := make([]byte, totalLen)

	// ICMP header
	message[0] = header.Type
	message[1] = header.Code
	// Checksum will be calculated later
	binary.BigEndian.PutUint16(message[4:6], header.ID)
	binary.BigEndian.PutUint16(message[6:8], header.Sequence)

	// Copy payload
	copy(message[headerLen:], payload)

	// Calculate and set checksum
	header.Checksum = pb.calculateICMPChecksum(message)
	binary.BigEndian.PutUint16(message[2:4], header.Checksum)

	return message
}

// calculateIPv4Checksum calculates IPv4 header checksum
func (pb *PacketBuilder) calculateIPv4Checksum(header []byte) uint16 {
	// Clear checksum field
	header[10] = 0
	header[11] = 0

	var sum uint32
	for i := 0; i < len(header); i += 2 {
		sum += uint32(binary.BigEndian.Uint16(header[i : i+2]))
	}

	// Add carry
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	return uint16(^sum)
}

// calculateICMPChecksum calculates ICMP checksum
func (pb *PacketBuilder) calculateICMPChecksum(message []byte) uint16 {
	// Clear checksum field
	message[2] = 0
	message[3] = 0

	var sum uint32
	
	// Sum all 16-bit words
	for i := 0; i < len(message)-1; i += 2 {
		sum += uint32(binary.BigEndian.Uint16(message[i : i+2]))
	}

	// Add odd byte if present
	if len(message)%2 == 1 {
		sum += uint32(message[len(message)-1]) << 8
	}

	// Add carry
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	return uint16(^sum)
}

// CalculateTCPChecksum calculates TCP checksum with pseudo header
func (pb *PacketBuilder) CalculateTCPChecksum(srcIP, dstIP net.IP, tcpData []byte) uint16 {
	var sum uint32

	// Add pseudo header
	if srcIP.To4() != nil {
		// IPv4 pseudo header
		sum += uint32(binary.BigEndian.Uint16(srcIP.To4()[0:2]))
		sum += uint32(binary.BigEndian.Uint16(srcIP.To4()[2:4]))
		sum += uint32(binary.BigEndian.Uint16(dstIP.To4()[0:2]))
		sum += uint32(binary.BigEndian.Uint16(dstIP.To4()[2:4]))
		sum += uint32(ProtocolTCP)
		sum += uint32(len(tcpData))
	} else {
		// IPv6 pseudo header
		for i := 0; i < 16; i += 2 {
			sum += uint32(binary.BigEndian.Uint16(srcIP.To16()[i : i+2]))
			sum += uint32(binary.BigEndian.Uint16(dstIP.To16()[i : i+2]))
		}
		sum += uint32(ProtocolTCP)
		sum += uint32(len(tcpData))
	}

	// Clear checksum field
	tcpData[16] = 0
	tcpData[17] = 0

	// Add TCP header and data
	for i := 0; i < len(tcpData)-1; i += 2 {
		sum += uint32(binary.BigEndian.Uint16(tcpData[i : i+2]))
	}

	// Add odd byte if present
	if len(tcpData)%2 == 1 {
		sum += uint32(tcpData[len(tcpData)-1]) << 8
	}

	// Add carry
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	return uint16(^sum)
}

// CalculateUDPChecksum calculates UDP checksum with pseudo header
func (pb *PacketBuilder) CalculateUDPChecksum(srcIP, dstIP net.IP, udpData []byte) uint16 {
	var sum uint32

	// Add pseudo header
	if srcIP.To4() != nil {
		// IPv4 pseudo header
		sum += uint32(binary.BigEndian.Uint16(srcIP.To4()[0:2]))
		sum += uint32(binary.BigEndian.Uint16(srcIP.To4()[2:4]))
		sum += uint32(binary.BigEndian.Uint16(dstIP.To4()[0:2]))
		sum += uint32(binary.BigEndian.Uint16(dstIP.To4()[2:4]))
		sum += uint32(ProtocolUDP)
		sum += uint32(len(udpData))
	} else {
		// IPv6 pseudo header
		for i := 0; i < 16; i += 2 {
			sum += uint32(binary.BigEndian.Uint16(srcIP.To16()[i : i+2]))
			sum += uint32(binary.BigEndian.Uint16(dstIP.To16()[i : i+2]))
		}
		sum += uint32(ProtocolUDP)
		sum += uint32(len(udpData))
	}

	// Clear checksum field
	udpData[6] = 0
	udpData[7] = 0

	// Add UDP header and data
	for i := 0; i < len(udpData)-1; i += 2 {
		sum += uint32(binary.BigEndian.Uint16(udpData[i : i+2]))
	}

	// Add odd byte if present
	if len(udpData)%2 == 1 {
		sum += uint32(udpData[len(udpData)-1]) << 8
	}

	// Add carry
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}

	checksum := uint16(^sum)
	if checksum == 0 {
		checksum = 0xFFFF // UDP checksum of 0 means no checksum
	}

	return checksum
}
