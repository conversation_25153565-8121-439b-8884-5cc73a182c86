package netstack

import (
	"encoding/binary"
	"fmt"
	"net"
)

// Protocol constants
const (
	ProtocolICMP = 1
	ProtocolTCP  = 6
	ProtocolUDP  = 17
	ProtocolICMPv6 = 58
)

// IPv4Header represents an IPv4 packet header
type IPv4Header struct {
	Version        uint8  // Version (4 bits) + IHL (4 bits)
	TypeOfService  uint8  // Type of Service
	TotalLength    uint16 // Total Length
	Identification uint16 // Identification
	Flags          uint16 // Flags (3 bits) + Fragment Offset (13 bits)
	TTL            uint8  // Time to Live
	Protocol       uint8  // Protocol
	Checksum       uint16 // Header Checksum
	SrcIP          net.IP // Source IP Address
	DstIP          net.IP // Destination IP Address
}

// IPv6Header represents an IPv6 packet header
type IPv6Header struct {
	Version      uint8  // Version (4 bits) + Traffic Class (8 bits) + Flow Label (20 bits)
	TrafficClass uint8  // Traffic Class
	FlowLabel    uint32 // Flow Label
	PayloadLen   uint16 // Payload Length
	NextHeader   uint8  // Next Header
	HopLimit     uint8  // Hop Limit
	SrcIP        net.IP // Source IP Address (16 bytes)
	DstIP        net.IP // Destination IP Address (16 bytes)
}

// TCPHeader represents a TCP header
type TCPHeader struct {
	SrcPort    uint16 // Source Port
	DstPort    uint16 // Destination Port
	SeqNum     uint32 // Sequence Number
	AckNum     uint32 // Acknowledgment Number
	DataOffset uint8  // Data Offset (4 bits) + Reserved (3 bits) + Flags (9 bits)
	Flags      uint8  // TCP Flags
	Window     uint16 // Window Size
	Checksum   uint16 // Checksum
	UrgentPtr  uint16 // Urgent Pointer
}

// UDPHeader represents a UDP header
type UDPHeader struct {
	SrcPort  uint16 // Source Port
	DstPort  uint16 // Destination Port
	Length   uint16 // Length
	Checksum uint16 // Checksum
}

// ICMPHeader represents an ICMP header
type ICMPHeader struct {
	Type     uint8  // Type
	Code     uint8  // Code
	Checksum uint16 // Checksum
	ID       uint16 // Identifier
	Sequence uint16 // Sequence Number
}

// Packet represents a parsed network packet
type Packet struct {
	// Raw packet data
	Raw []byte

	// Headers
	IPv4Header *IPv4Header
	IPv6Header *IPv6Header
	TCPHeader  *TCPHeader
	UDPHeader  *UDPHeader
	ICMPHeader *ICMPHeader

	// Payload
	Payload []byte

	// Metadata
	IsIPv4 bool
	IsIPv6 bool
}

// ParseIPv4Packet parses an IPv4 packet
func ParseIPv4Packet(data []byte) (*Packet, error) {
	if len(data) < 20 {
		return nil, fmt.Errorf("packet too short for IPv4 header")
	}

	packet := &Packet{
		Raw:    data,
		IsIPv4: true,
	}

	// Parse IPv4 header
	header := &IPv4Header{}
	header.Version = data[0] >> 4
	if header.Version != 4 {
		return nil, fmt.Errorf("not an IPv4 packet")
	}

	ihl := data[0] & 0x0F
	headerLen := int(ihl) * 4
	if headerLen < 20 || headerLen > len(data) {
		return nil, fmt.Errorf("invalid IPv4 header length")
	}

	header.TypeOfService = data[1]
	header.TotalLength = binary.BigEndian.Uint16(data[2:4])
	header.Identification = binary.BigEndian.Uint16(data[4:6])
	header.Flags = binary.BigEndian.Uint16(data[6:8])
	header.TTL = data[8]
	header.Protocol = data[9]
	header.Checksum = binary.BigEndian.Uint16(data[10:12])
	header.SrcIP = net.IP(data[12:16])
	header.DstIP = net.IP(data[16:20])

	packet.IPv4Header = header

	// Parse transport layer
	if len(data) > headerLen {
		transportData := data[headerLen:]
		if err := packet.parseTransportLayer(header.Protocol, transportData); err != nil {
			return nil, fmt.Errorf("failed to parse transport layer: %w", err)
		}
	}

	return packet, nil
}

// ParseIPv6Packet parses an IPv6 packet
func ParseIPv6Packet(data []byte) (*Packet, error) {
	if len(data) < 40 {
		return nil, fmt.Errorf("packet too short for IPv6 header")
	}

	packet := &Packet{
		Raw:    data,
		IsIPv6: true,
	}

	// Parse IPv6 header
	header := &IPv6Header{}
	header.Version = data[0] >> 4
	if header.Version != 6 {
		return nil, fmt.Errorf("not an IPv6 packet")
	}

	header.TrafficClass = ((data[0] & 0x0F) << 4) | (data[1] >> 4)
	header.FlowLabel = (uint32(data[1]&0x0F) << 16) | (uint32(data[2]) << 8) | uint32(data[3])
	header.PayloadLen = binary.BigEndian.Uint16(data[4:6])
	header.NextHeader = data[6]
	header.HopLimit = data[7]
	header.SrcIP = net.IP(data[8:24])
	header.DstIP = net.IP(data[24:40])

	packet.IPv6Header = header

	// Parse transport layer
	if len(data) > 40 {
		transportData := data[40:]
		if err := packet.parseTransportLayer(header.NextHeader, transportData); err != nil {
			return nil, fmt.Errorf("failed to parse transport layer: %w", err)
		}
	}

	return packet, nil
}

// parseTransportLayer parses the transport layer (TCP/UDP/ICMP)
func (p *Packet) parseTransportLayer(protocol uint8, data []byte) error {
	switch protocol {
	case ProtocolTCP:
		return p.parseTCP(data)
	case ProtocolUDP:
		return p.parseUDP(data)
	case ProtocolICMP, ProtocolICMPv6:
		return p.parseICMP(data)
	default:
		// Unknown protocol, store as payload
		p.Payload = data
		return nil
	}
}

// parseTCP parses TCP header
func (p *Packet) parseTCP(data []byte) error {
	if len(data) < 20 {
		return fmt.Errorf("TCP header too short")
	}

	header := &TCPHeader{}
	header.SrcPort = binary.BigEndian.Uint16(data[0:2])
	header.DstPort = binary.BigEndian.Uint16(data[2:4])
	header.SeqNum = binary.BigEndian.Uint32(data[4:8])
	header.AckNum = binary.BigEndian.Uint32(data[8:12])
	header.DataOffset = data[12] >> 4
	header.Flags = data[13]
	header.Window = binary.BigEndian.Uint16(data[14:16])
	header.Checksum = binary.BigEndian.Uint16(data[16:18])
	header.UrgentPtr = binary.BigEndian.Uint16(data[18:20])

	p.TCPHeader = header

	// Calculate payload offset
	headerLen := int(header.DataOffset) * 4
	if headerLen < 20 || headerLen > len(data) {
		return fmt.Errorf("invalid TCP header length")
	}

	if len(data) > headerLen {
		p.Payload = data[headerLen:]
	}

	return nil
}

// parseUDP parses UDP header
func (p *Packet) parseUDP(data []byte) error {
	if len(data) < 8 {
		return fmt.Errorf("UDP header too short")
	}

	header := &UDPHeader{}
	header.SrcPort = binary.BigEndian.Uint16(data[0:2])
	header.DstPort = binary.BigEndian.Uint16(data[2:4])
	header.Length = binary.BigEndian.Uint16(data[4:6])
	header.Checksum = binary.BigEndian.Uint16(data[6:8])

	p.UDPHeader = header

	if len(data) > 8 {
		p.Payload = data[8:]
	}

	return nil
}

// parseICMP parses ICMP header
func (p *Packet) parseICMP(data []byte) error {
	if len(data) < 8 {
		return fmt.Errorf("ICMP header too short")
	}

	header := &ICMPHeader{}
	header.Type = data[0]
	header.Code = data[1]
	header.Checksum = binary.BigEndian.Uint16(data[2:4])
	header.ID = binary.BigEndian.Uint16(data[4:6])
	header.Sequence = binary.BigEndian.Uint16(data[6:8])

	p.ICMPHeader = header

	if len(data) > 8 {
		p.Payload = data[8:]
	}

	return nil
}
