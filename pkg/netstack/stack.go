package netstack

import (
	"fmt"
	"net"
	"sync"

	"tailscale-nat46/pkg/logger"
)

// Stack represents a user-space network stack
type Stack struct {
	logger  *logger.Logger
	builder *PacketBuilder

	// Configuration
	localIPv4 net.IP
	localIPv6 net.IP

	// State
	running bool
	mu      sync.RWMutex

	// Packet processing channels
	inbound  chan *Packet
	outbound chan []byte

	// Connection tracking
	connections map[string]*Connection
	connMu      sync.RWMutex
}

// Connection represents a tracked connection
type Connection struct {
	ID        string
	Protocol  uint8
	SrcIP     net.IP
	DstIP     net.IP
	SrcPort   uint16
	DstPort   uint16
	State     ConnectionState
	CreatedAt int64
	LastSeen  int64
}

// ConnectionState represents the state of a connection
type ConnectionState int

const (
	StateNew ConnectionState = iota
	StateEstablished
	StateClosing
	StateClosed
)

// Config represents the configuration for the network stack
type Config struct {
	LocalIPv4 string
	LocalIPv6 string
	Logger    *logger.Logger
}

// NewStack creates a new network stack
func NewStack(cfg *Config) (*Stack, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	if cfg.Logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	// Parse IP addresses
	localIPv4 := net.ParseIP(cfg.LocalIPv4)
	if localIPv4 == nil {
		return nil, fmt.Errorf("invalid local IPv4 address: %s", cfg.LocalIPv4)
	}

	localIPv6 := net.ParseIP(cfg.LocalIPv6)
	if localIPv6 == nil {
		return nil, fmt.Errorf("invalid local IPv6 address: %s", cfg.LocalIPv6)
	}

	stack := &Stack{
		logger:      cfg.Logger,
		builder:     NewPacketBuilder(),
		localIPv4:   localIPv4,
		localIPv6:   localIPv6,
		inbound:     make(chan *Packet, 256),
		outbound:    make(chan []byte, 256),
		connections: make(map[string]*Connection),
	}

	cfg.Logger.Info("Network stack initialized: IPv4=%s, IPv6=%s", cfg.LocalIPv4, cfg.LocalIPv6)

	return stack, nil
}

// Start starts the network stack
func (s *Stack) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return fmt.Errorf("stack is already running")
	}

	s.logger.Info("Starting network stack")
	s.running = true

	return nil
}

// Stop stops the network stack
func (s *Stack) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return nil
	}

	s.logger.Info("Stopping network stack")
	s.running = false

	// Close channels
	close(s.inbound)
	close(s.outbound)

	return nil
}

// ProcessInboundPacket processes an inbound packet (from Wintun interface)
func (s *Stack) ProcessInboundPacket(data []byte) (*Packet, error) {
	if len(data) == 0 {
		return nil, fmt.Errorf("empty packet")
	}

	// Determine IP version
	version := data[0] >> 4

	var packet *Packet
	var err error

	switch version {
	case 4:
		packet, err = ParseIPv4Packet(data)
		if err != nil {
			return nil, fmt.Errorf("failed to parse IPv4 packet: %w", err)
		}
	case 6:
		packet, err = ParseIPv6Packet(data)
		if err != nil {
			return nil, fmt.Errorf("failed to parse IPv6 packet: %w", err)
		}
	default:
		return nil, fmt.Errorf("unsupported IP version: %d", version)
	}

	// Update connection tracking
	s.updateConnection(packet)

	s.logger.Debug("Processed inbound packet: version=%d, protocol=%d, len=%d",
		version, s.getProtocol(packet), len(data))

	return packet, nil
}

// BuildIPv4Response builds an IPv4 response packet
func (s *Stack) BuildIPv4Response(srcIP, dstIP net.IP, protocol uint8, payload []byte) []byte {
	header := &IPv4Header{
		Version:        4,
		TypeOfService:  0,
		Identification: 0, // Will be set by OS
		Flags:          0x4000, // Don't fragment
		TTL:            64,
		Protocol:       protocol,
		SrcIP:          srcIP,
		DstIP:          dstIP,
	}

	return s.builder.BuildIPv4Packet(header, payload)
}

// BuildIPv6Packet builds an IPv6 packet
func (s *Stack) BuildIPv6Packet(srcIP, dstIP net.IP, nextHeader uint8, payload []byte) []byte {
	header := &IPv6Header{
		Version:      6,
		TrafficClass: 0,
		FlowLabel:    0,
		NextHeader:   nextHeader,
		HopLimit:     64,
		SrcIP:        srcIP,
		DstIP:        dstIP,
	}

	return s.builder.BuildIPv6Packet(header, payload)
}

// BuildTCPResponse builds a TCP response segment
func (s *Stack) BuildTCPResponse(srcPort, dstPort uint16, seqNum, ackNum uint32, flags uint8, payload []byte) []byte {
	header := &TCPHeader{
		SrcPort:    srcPort,
		DstPort:    dstPort,
		SeqNum:     seqNum,
		AckNum:     ackNum,
		DataOffset: 5, // 20 bytes
		Flags:      flags,
		Window:     65535,
		Checksum:   0, // Will be calculated by caller
		UrgentPtr:  0,
	}

	return s.builder.BuildTCPSegment(header, payload)
}

// BuildUDPResponse builds a UDP response datagram
func (s *Stack) BuildUDPResponse(srcPort, dstPort uint16, payload []byte) []byte {
	header := &UDPHeader{
		SrcPort:  srcPort,
		DstPort:  dstPort,
		Length:   0, // Will be calculated by builder
		Checksum: 0, // Will be calculated by caller
	}

	return s.builder.BuildUDPDatagram(header, payload)
}

// BuildICMPResponse builds an ICMP response message
func (s *Stack) BuildICMPResponse(icmpType, code uint8, id, sequence uint16, payload []byte) []byte {
	header := &ICMPHeader{
		Type:     icmpType,
		Code:     code,
		Checksum: 0, // Will be calculated by builder
		ID:       id,
		Sequence: sequence,
	}

	return s.builder.BuildICMPMessage(header, payload)
}

// CalculateTCPChecksum calculates TCP checksum
func (s *Stack) CalculateTCPChecksum(srcIP, dstIP net.IP, tcpData []byte) uint16 {
	return s.builder.CalculateTCPChecksum(srcIP, dstIP, tcpData)
}

// CalculateUDPChecksum calculates UDP checksum
func (s *Stack) CalculateUDPChecksum(srcIP, dstIP net.IP, udpData []byte) uint16 {
	return s.builder.CalculateUDPChecksum(srcIP, dstIP, udpData)
}

// GetLocalIPv4 returns the local IPv4 address
func (s *Stack) GetLocalIPv4() net.IP {
	return s.localIPv4
}

// GetLocalIPv6 returns the local IPv6 address
func (s *Stack) GetLocalIPv6() net.IP {
	return s.localIPv6
}

// updateConnection updates connection tracking
func (s *Stack) updateConnection(packet *Packet) {
	connID := s.getConnectionID(packet)
	if connID == "" {
		return
	}

	s.connMu.Lock()
	defer s.connMu.Unlock()

	conn, exists := s.connections[connID]
	if !exists {
		conn = &Connection{
			ID:       connID,
			Protocol: s.getProtocol(packet),
			State:    StateNew,
		}

		if packet.IsIPv4 {
			conn.SrcIP = packet.IPv4Header.SrcIP
			conn.DstIP = packet.IPv4Header.DstIP
		} else if packet.IsIPv6 {
			conn.SrcIP = packet.IPv6Header.SrcIP
			conn.DstIP = packet.IPv6Header.DstIP
		}

		if packet.TCPHeader != nil {
			conn.SrcPort = packet.TCPHeader.SrcPort
			conn.DstPort = packet.TCPHeader.DstPort
		} else if packet.UDPHeader != nil {
			conn.SrcPort = packet.UDPHeader.SrcPort
			conn.DstPort = packet.UDPHeader.DstPort
		}

		s.connections[connID] = conn
	}

	// Update last seen time
	conn.LastSeen = getCurrentTime()

	// Update state based on TCP flags
	if packet.TCPHeader != nil {
		s.updateTCPState(conn, packet.TCPHeader)
	}
}

// getConnectionID generates a connection ID
func (s *Stack) getConnectionID(packet *Packet) string {
	var srcIP, dstIP net.IP
	var srcPort, dstPort uint16
	var protocol uint8

	if packet.IsIPv4 {
		srcIP = packet.IPv4Header.SrcIP
		dstIP = packet.IPv4Header.DstIP
		protocol = packet.IPv4Header.Protocol
	} else if packet.IsIPv6 {
		srcIP = packet.IPv6Header.SrcIP
		dstIP = packet.IPv6Header.DstIP
		protocol = packet.IPv6Header.NextHeader
	} else {
		return ""
	}

	if packet.TCPHeader != nil {
		srcPort = packet.TCPHeader.SrcPort
		dstPort = packet.TCPHeader.DstPort
	} else if packet.UDPHeader != nil {
		srcPort = packet.UDPHeader.SrcPort
		dstPort = packet.UDPHeader.DstPort
	}

	return fmt.Sprintf("%s:%d-%s:%d-%d", srcIP.String(), srcPort, dstIP.String(), dstPort, protocol)
}

// getProtocol returns the protocol from a packet
func (s *Stack) getProtocol(packet *Packet) uint8 {
	if packet.IsIPv4 {
		return packet.IPv4Header.Protocol
	} else if packet.IsIPv6 {
		return packet.IPv6Header.NextHeader
	}
	return 0
}

// updateTCPState updates TCP connection state
func (s *Stack) updateTCPState(conn *Connection, tcpHeader *TCPHeader) {
	// Simplified TCP state tracking
	if tcpHeader.Flags&0x02 != 0 { // SYN
		conn.State = StateNew
	} else if tcpHeader.Flags&0x10 != 0 { // ACK
		if conn.State == StateNew {
			conn.State = StateEstablished
		}
	} else if tcpHeader.Flags&0x01 != 0 { // FIN
		conn.State = StateClosing
	} else if tcpHeader.Flags&0x04 != 0 { // RST
		conn.State = StateClosed
	}
}

// getCurrentTime returns current timestamp (simplified)
func getCurrentTime() int64 {
	return 0 // TODO: Implement proper timestamp
}
