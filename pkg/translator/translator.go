package translator

import (
	"encoding/binary"
	"fmt"
	"net"
	"sync"
	"time"

	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/netstack"
)

// Translator handles IPv4 to IPv6 packet translation
type Translator struct {
	logger *logger.Logger

	// Configuration
	localIPv4 net.IP
	localIPv6 net.IP
	mappings  map[string]string // IPv4 -> IPv6 mappings

	// Network stack
	netStack *netstack.Stack

	// Connection tracking
	sessions map[string]*Session
	sessMu   sync.RWMutex

	// State
	running bool
	mu      sync.RWMutex
}

// Session represents a translation session
type Session struct {
	ID        string
	IPv4Src   net.IP
	IPv4Dst   net.IP
	IPv6Src   net.IP
	IPv6Dst   net.IP
	Protocol  uint8
	SrcPort   uint16
	DstPort   uint16
	CreatedAt time.Time
	LastSeen  time.Time
	State     SessionState
}

// SessionState represents the state of a translation session
type SessionState int

const (
	SessionActive SessionState = iota
	SessionClosing
	SessionClosed
)

// Config represents the configuration for the translator
type Config struct {
	LocalIPv4 string
	LocalIPv6 string
	Mappings  map[string]string
	Logger    *logger.Logger
	NetStack  *netstack.Stack
}

// NewTranslator creates a new packet translator
func NewTranslator(cfg *Config) (*Translator, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	if cfg.Logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	if cfg.NetStack == nil {
		return nil, fmt.Errorf("network stack cannot be nil")
	}

	// Parse IP addresses
	localIPv4 := net.ParseIP(cfg.LocalIPv4)
	if localIPv4 == nil {
		return nil, fmt.Errorf("invalid local IPv4 address: %s", cfg.LocalIPv4)
	}

	localIPv6 := net.ParseIP(cfg.LocalIPv6)
	if localIPv6 == nil {
		return nil, fmt.Errorf("invalid local IPv6 address: %s", cfg.LocalIPv6)
	}

	// Validate mappings
	if cfg.Mappings == nil || len(cfg.Mappings) == 0 {
		return nil, fmt.Errorf("no IPv4 to IPv6 mappings provided")
	}

	for ipv4, ipv6 := range cfg.Mappings {
		if net.ParseIP(ipv4) == nil {
			return nil, fmt.Errorf("invalid IPv4 address in mapping: %s", ipv4)
		}
		if net.ParseIP(ipv6) == nil {
			return nil, fmt.Errorf("invalid IPv6 address in mapping: %s", ipv6)
		}
	}

	translator := &Translator{
		logger:    cfg.Logger,
		localIPv4: localIPv4,
		localIPv6: localIPv6,
		mappings:  cfg.Mappings,
		netStack:  cfg.NetStack,
		sessions:  make(map[string]*Session),
	}

	cfg.Logger.Info("Translator initialized with %d mappings", len(cfg.Mappings))

	return translator, nil
}

// Start starts the translator
func (t *Translator) Start() error {
	t.mu.Lock()
	defer t.mu.Unlock()

	if t.running {
		return fmt.Errorf("translator is already running")
	}

	t.logger.Info("Starting packet translator")
	t.running = true

	return nil
}

// Stop stops the translator
func (t *Translator) Stop() error {
	t.mu.Lock()
	defer t.mu.Unlock()

	if !t.running {
		return nil
	}

	t.logger.Info("Stopping packet translator")
	t.running = false

	return nil
}

// TranslateIPv4ToIPv6 translates an IPv4 packet to IPv6
func (t *Translator) TranslateIPv4ToIPv6(packet *netstack.Packet) ([]byte, error) {
	if !packet.IsIPv4 {
		return nil, fmt.Errorf("packet is not IPv4")
	}

	// Check if destination IP has a mapping
	dstIPv4 := packet.IPv4Header.DstIP.String()
	dstIPv6Str, exists := t.mappings[dstIPv4]
	if !exists {
		return nil, fmt.Errorf("no IPv6 mapping for IPv4 address: %s", dstIPv4)
	}

	dstIPv6 := net.ParseIP(dstIPv6Str)
	if dstIPv6 == nil {
		return nil, fmt.Errorf("invalid IPv6 address in mapping: %s", dstIPv6Str)
	}

	// Create or update session
	session := t.getOrCreateSession(packet, dstIPv6)

	// Translate based on protocol
	var payload []byte
	var nextHeader uint8
	var err error

	switch packet.IPv4Header.Protocol {
	case netstack.ProtocolTCP:
		payload, err = t.translateTCP(packet, session)
		nextHeader = netstack.ProtocolTCP
	case netstack.ProtocolUDP:
		payload, err = t.translateUDP(packet, session)
		nextHeader = netstack.ProtocolUDP
	case netstack.ProtocolICMP:
		payload, err = t.translateICMP(packet, session)
		nextHeader = netstack.ProtocolICMPv6
	default:
		return nil, fmt.Errorf("unsupported protocol: %d", packet.IPv4Header.Protocol)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to translate protocol payload: %w", err)
	}

	// Build IPv6 packet
	ipv6Packet := t.netStack.BuildIPv6Packet(t.localIPv6, dstIPv6, nextHeader, payload)

	t.logger.Debug("Translated IPv4 to IPv6: %s -> %s, protocol=%d, len=%d",
		packet.IPv4Header.SrcIP.String(), dstIPv6.String(), packet.IPv4Header.Protocol, len(ipv6Packet))

	return ipv6Packet, nil
}

// TranslateIPv6ToIPv4 translates an IPv6 packet to IPv4
func (t *Translator) TranslateIPv6ToIPv4(packet *netstack.Packet) ([]byte, error) {
	if !packet.IsIPv6 {
		return nil, fmt.Errorf("packet is not IPv6")
	}

	// Find the session for this packet
	session := t.findSessionForIPv6(packet)
	if session == nil {
		return nil, fmt.Errorf("no session found for IPv6 packet")
	}

	// Translate based on protocol
	var payload []byte
	var protocol uint8
	var err error

	switch packet.IPv6Header.NextHeader {
	case netstack.ProtocolTCP:
		payload, err = t.translateTCPResponse(packet, session)
		protocol = netstack.ProtocolTCP
	case netstack.ProtocolUDP:
		payload, err = t.translateUDPResponse(packet, session)
		protocol = netstack.ProtocolUDP
	case netstack.ProtocolICMPv6:
		payload, err = t.translateICMPResponse(packet, session)
		protocol = netstack.ProtocolICMP
	default:
		return nil, fmt.Errorf("unsupported protocol: %d", packet.IPv6Header.NextHeader)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to translate protocol payload: %w", err)
	}

	// Build IPv4 packet
	ipv4Packet := t.netStack.BuildIPv4Response(session.IPv4Dst, session.IPv4Src, protocol, payload)

	t.logger.Debug("Translated IPv6 to IPv4: %s -> %s, protocol=%d, len=%d",
		packet.IPv6Header.SrcIP.String(), session.IPv4Src.String(), packet.IPv6Header.NextHeader, len(ipv4Packet))

	return ipv4Packet, nil
}

// getOrCreateSession gets or creates a translation session
func (t *Translator) getOrCreateSession(packet *netstack.Packet, dstIPv6 net.IP) *Session {
	sessionID := t.generateSessionID(packet)

	t.sessMu.Lock()
	defer t.sessMu.Unlock()

	session, exists := t.sessions[sessionID]
	if !exists {
		session = &Session{
			ID:        sessionID,
			IPv4Src:   packet.IPv4Header.SrcIP,
			IPv4Dst:   packet.IPv4Header.DstIP,
			IPv6Src:   t.localIPv6,
			IPv6Dst:   dstIPv6,
			Protocol:  packet.IPv4Header.Protocol,
			CreatedAt: time.Now(),
			State:     SessionActive,
		}

		if packet.TCPHeader != nil {
			session.SrcPort = packet.TCPHeader.SrcPort
			session.DstPort = packet.TCPHeader.DstPort
		} else if packet.UDPHeader != nil {
			session.SrcPort = packet.UDPHeader.SrcPort
			session.DstPort = packet.UDPHeader.DstPort
		}

		t.sessions[sessionID] = session
		t.logger.Debug("Created new session: %s", sessionID)
	}

	session.LastSeen = time.Now()
	return session
}

// findSessionForIPv6 finds a session for an IPv6 packet
func (t *Translator) findSessionForIPv6(packet *netstack.Packet) *Session {
	t.sessMu.RLock()
	defer t.sessMu.RUnlock()

	var srcPort, dstPort uint16
	if packet.TCPHeader != nil {
		srcPort = packet.TCPHeader.SrcPort
		dstPort = packet.TCPHeader.DstPort
	} else if packet.UDPHeader != nil {
		srcPort = packet.UDPHeader.SrcPort
		dstPort = packet.UDPHeader.DstPort
	}

	for _, session := range t.sessions {
		if session.IPv6Dst.Equal(packet.IPv6Header.SrcIP) &&
			session.IPv6Src.Equal(packet.IPv6Header.DstIP) &&
			session.Protocol == packet.IPv6Header.NextHeader &&
			session.SrcPort == dstPort &&
			session.DstPort == srcPort {
			return session
		}
	}

	return nil
}

// generateSessionID generates a unique session ID
func (t *Translator) generateSessionID(packet *netstack.Packet) string {
	var srcPort, dstPort uint16
	if packet.TCPHeader != nil {
		srcPort = packet.TCPHeader.SrcPort
		dstPort = packet.TCPHeader.DstPort
	} else if packet.UDPHeader != nil {
		srcPort = packet.UDPHeader.SrcPort
		dstPort = packet.UDPHeader.DstPort
	}

	return fmt.Sprintf("%s:%d-%s:%d-%d",
		packet.IPv4Header.SrcIP.String(), srcPort,
		packet.IPv4Header.DstIP.String(), dstPort,
		packet.IPv4Header.Protocol)
}

// translateTCP translates TCP payload
func (t *Translator) translateTCP(packet *netstack.Packet, session *Session) ([]byte, error) {
	if packet.TCPHeader == nil {
		return nil, fmt.Errorf("TCP header is nil")
	}

	// Build TCP segment with original data
	tcpData := t.netStack.BuildTCPResponse(
		packet.TCPHeader.SrcPort,
		packet.TCPHeader.DstPort,
		packet.TCPHeader.SeqNum,
		packet.TCPHeader.AckNum,
		packet.TCPHeader.Flags,
		packet.Payload,
	)

	// Calculate TCP checksum for IPv6
	checksum := t.netStack.CalculateTCPChecksum(session.IPv6Src, session.IPv6Dst, tcpData)
	binary.BigEndian.PutUint16(tcpData[16:18], checksum)

	return tcpData, nil
}

// translateUDP translates UDP payload
func (t *Translator) translateUDP(packet *netstack.Packet, session *Session) ([]byte, error) {
	if packet.UDPHeader == nil {
		return nil, fmt.Errorf("UDP header is nil")
	}

	// Build UDP datagram with original data
	udpData := t.netStack.BuildUDPResponse(
		packet.UDPHeader.SrcPort,
		packet.UDPHeader.DstPort,
		packet.Payload,
	)

	// Calculate UDP checksum for IPv6
	checksum := t.netStack.CalculateUDPChecksum(session.IPv6Src, session.IPv6Dst, udpData)
	binary.BigEndian.PutUint16(udpData[6:8], checksum)

	return udpData, nil
}

// translateICMP translates ICMP to ICMPv6
func (t *Translator) translateICMP(packet *netstack.Packet, session *Session) ([]byte, error) {
	if packet.ICMPHeader == nil {
		return nil, fmt.Errorf("ICMP header is nil")
	}

	// Translate ICMP type to ICMPv6
	var icmpv6Type uint8
	switch packet.ICMPHeader.Type {
	case 8: // Echo Request
		icmpv6Type = 128
	case 0: // Echo Reply
		icmpv6Type = 129
	default:
		return nil, fmt.Errorf("unsupported ICMP type: %d", packet.ICMPHeader.Type)
	}

	// Build ICMPv6 message
	icmpData := t.netStack.BuildICMPResponse(
		icmpv6Type,
		packet.ICMPHeader.Code,
		packet.ICMPHeader.ID,
		packet.ICMPHeader.Sequence,
		packet.Payload,
	)

	return icmpData, nil
}

// translateTCPResponse translates TCP response payload
func (t *Translator) translateTCPResponse(packet *netstack.Packet, session *Session) ([]byte, error) {
	if packet.TCPHeader == nil {
		return nil, fmt.Errorf("TCP header is nil")
	}

	// Build TCP segment with swapped ports
	tcpData := t.netStack.BuildTCPResponse(
		packet.TCPHeader.DstPort, // Swap ports for response
		packet.TCPHeader.SrcPort,
		packet.TCPHeader.SeqNum,
		packet.TCPHeader.AckNum,
		packet.TCPHeader.Flags,
		packet.Payload,
	)

	// Calculate TCP checksum for IPv4
	checksum := t.netStack.CalculateTCPChecksum(session.IPv4Dst, session.IPv4Src, tcpData)
	binary.BigEndian.PutUint16(tcpData[16:18], checksum)

	return tcpData, nil
}

// translateUDPResponse translates UDP response payload
func (t *Translator) translateUDPResponse(packet *netstack.Packet, session *Session) ([]byte, error) {
	if packet.UDPHeader == nil {
		return nil, fmt.Errorf("UDP header is nil")
	}

	// Build UDP datagram with swapped ports
	udpData := t.netStack.BuildUDPResponse(
		packet.UDPHeader.DstPort, // Swap ports for response
		packet.UDPHeader.SrcPort,
		packet.Payload,
	)

	// Calculate UDP checksum for IPv4
	checksum := t.netStack.CalculateUDPChecksum(session.IPv4Dst, session.IPv4Src, udpData)
	binary.BigEndian.PutUint16(udpData[6:8], checksum)

	return udpData, nil
}

// translateICMPResponse translates ICMPv6 to ICMP
func (t *Translator) translateICMPResponse(packet *netstack.Packet, session *Session) ([]byte, error) {
	if packet.ICMPHeader == nil {
		return nil, fmt.Errorf("ICMP header is nil")
	}

	// Translate ICMPv6 type to ICMP
	var icmpType uint8
	switch packet.ICMPHeader.Type {
	case 128: // Echo Request
		icmpType = 8
	case 129: // Echo Reply
		icmpType = 0
	default:
		return nil, fmt.Errorf("unsupported ICMPv6 type: %d", packet.ICMPHeader.Type)
	}

	// Build ICMP message
	icmpData := t.netStack.BuildICMPResponse(
		icmpType,
		packet.ICMPHeader.Code,
		packet.ICMPHeader.ID,
		packet.ICMPHeader.Sequence,
		packet.Payload,
	)

	return icmpData, nil
}

// CleanupSessions removes old sessions
func (t *Translator) CleanupSessions(maxAge time.Duration) {
	t.sessMu.Lock()
	defer t.sessMu.Unlock()

	now := time.Now()
	var toDelete []string

	for id, session := range t.sessions {
		if now.Sub(session.LastSeen) > maxAge {
			toDelete = append(toDelete, id)
		}
	}

	for _, id := range toDelete {
		delete(t.sessions, id)
		t.logger.Debug("Cleaned up session: %s", id)
	}

	if len(toDelete) > 0 {
		t.logger.Info("Cleaned up %d old sessions", len(toDelete))
	}
}

// GetSessionCount returns the number of active sessions
func (t *Translator) GetSessionCount() int {
	t.sessMu.RLock()
	defer t.sessMu.RUnlock()
	return len(t.sessions)
}

// GetMappings returns the IPv4 to IPv6 mappings
func (t *Translator) GetMappings() map[string]string {
	return t.mappings
}
