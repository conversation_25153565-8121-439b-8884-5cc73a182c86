package config

import (
	"fmt"
	"net"
	"os"

	"gopkg.in/yaml.v3"
)

// PortMapping represents a port forwarding rule
type PortMapping struct {
	ListenPort int    `yaml:"listen_port"`
	Protocol   string `yaml:"protocol"` // "tcp", "udp", or "both"
}

// Config represents the application configuration
type Config struct {
	// Local IPv4 address for the virtual interface (target IP)
	LocalIPv4 string `yaml:"local_ipv4"`

	// Port mappings for forwarding
	PortMappings []PortMapping `yaml:"port_mappings"`

	// Virtual interface name
	InterfaceName string `yaml:"interface_name"`

	// Logging configuration
	LogLevel string `yaml:"log_level"`
	LogFile  string `yaml:"log_file"`

	// Network configuration
	MTU int `yaml:"mtu"`

	// Advanced options
	BufferSize int  `yaml:"buffer_size"`
	Workers    int  `yaml:"workers"`
	Debug      bool `yaml:"debug"`
}

// DefaultConfig returns a configuration with default values
func DefaultConfig() *Config {
	return &Config{
		LocalIPv4: "************",
		PortMappings: []PortMapping{
			{ListenPort: 8080, Protocol: "tcp"},
		},
		InterfaceName: "goproxy",
		LogLevel:      "info",
		LogFile:       "",
		MTU:           1500,
		BufferSize:    65536,
		Workers:       4,
		Debug:         false,
	}
}

// LoadConfig loads configuration from a YAML file
func LoadConfig(filename string) (*Config, error) {
	cfg := DefaultConfig()

	// If file doesn't exist, return default config
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return cfg, nil
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	if err := yaml.Unmarshal(data, cfg); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return cfg, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate local IPv4 address
	if c.LocalIPv4 == "" {
		return fmt.Errorf("local_ipv4 is required")
	}
	if ip := net.ParseIP(c.LocalIPv4); ip == nil || ip.To4() == nil {
		return fmt.Errorf("invalid local_ipv4 address: %s", c.LocalIPv4)
	}

	// Validate port mappings
	if len(c.PortMappings) == 0 {
		return fmt.Errorf("at least one port mapping is required")
	}

	for i, mapping := range c.PortMappings {
		// Validate port number
		if mapping.ListenPort < 1 || mapping.ListenPort > 65535 {
			return fmt.Errorf("invalid port number in mapping %d: %d (must be between 1 and 65535)", i, mapping.ListenPort)
		}

		// Validate protocol
		if mapping.Protocol != "tcp" && mapping.Protocol != "udp" && mapping.Protocol != "both" {
			return fmt.Errorf("invalid protocol in mapping %d: %s (must be tcp, udp, or both)", i, mapping.Protocol)
		}
	}

	// Validate interface name
	if c.InterfaceName == "" {
		return fmt.Errorf("interface_name is required")
	}

	// Validate log level
	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	if !validLogLevels[c.LogLevel] {
		return fmt.Errorf("invalid log_level: %s (must be debug, info, warn, or error)", c.LogLevel)
	}

	// Validate MTU
	if c.MTU < 576 || c.MTU > 9000 {
		return fmt.Errorf("invalid MTU: %d (must be between 576 and 9000)", c.MTU)
	}

	// Validate buffer size
	if c.BufferSize < 1024 {
		return fmt.Errorf("invalid buffer_size: %d (must be at least 1024)", c.BufferSize)
	}

	// Validate workers
	if c.Workers < 1 || c.Workers > 32 {
		return fmt.Errorf("invalid workers: %d (must be between 1 and 32)", c.Workers)
	}

	return nil
}

// SaveConfig saves the configuration to a YAML file
func (c *Config) SaveConfig(filename string) error {
	data, err := yaml.Marshal(c)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}
