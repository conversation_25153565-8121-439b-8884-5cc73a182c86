package config

import (
	"fmt"
	"net"
	"os"

	"gopkg.in/yaml.v3"
)

// Config represents the application configuration
type Config struct {
	// Local IPv4 address for the virtual interface
	LocalIPv4 string `yaml:"local_ipv4"`

	// IPv4 to IPv6 mappings
	Mappings map[string]string `yaml:"mappings"`

	// Virtual interface name
	InterfaceName string `yaml:"interface_name"`

	// Logging configuration
	LogLevel string `yaml:"log_level"`
	LogFile  string `yaml:"log_file"`

	// Network configuration
	MTU int `yaml:"mtu"`

	// Advanced options
	BufferSize int  `yaml:"buffer_size"`
	Workers    int  `yaml:"workers"`
	Debug      bool `yaml:"debug"`
}

// DefaultConfig returns a configuration with default values
func DefaultConfig() *Config {
	return &Config{
		LocalIPv4: "************",
		Mappings: map[string]string{
			"********": "2001:db8::100",
		},
		InterfaceName: "goproxy",
		LogLevel:      "info",
		LogFile:       "",
		MTU:           1500,
		BufferSize:    65536,
		Workers:       4,
		Debug:         false,
	}
}

// LoadConfig loads configuration from a YAML file
func LoadConfig(filename string) (*Config, error) {
	cfg := DefaultConfig()

	// If file doesn't exist, return default config
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return cfg, nil
	}

	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	if err := yaml.Unmarshal(data, cfg); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// Validate configuration
	if err := cfg.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return cfg, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate local IPv4 address
	if c.LocalIPv4 == "" {
		return fmt.Errorf("local_ipv4 is required")
	}
	if ip := net.ParseIP(c.LocalIPv4); ip == nil || ip.To4() == nil {
		return fmt.Errorf("invalid local_ipv4 address: %s", c.LocalIPv4)
	}

	// Validate mappings
	if len(c.Mappings) == 0 {
		return fmt.Errorf("at least one IP mapping is required")
	}

	for ipv4, ipv6 := range c.Mappings {
		// Validate IPv4 address
		if ip := net.ParseIP(ipv4); ip == nil || ip.To4() == nil {
			return fmt.Errorf("invalid IPv4 address in mapping: %s", ipv4)
		}

		// Validate IPv6 address
		if ip := net.ParseIP(ipv6); ip == nil || ip.To4() != nil {
			return fmt.Errorf("invalid IPv6 address in mapping: %s", ipv6)
		}
	}

	// Validate interface name
	if c.InterfaceName == "" {
		return fmt.Errorf("interface_name is required")
	}

	// Validate log level
	validLogLevels := map[string]bool{
		"debug": true,
		"info":  true,
		"warn":  true,
		"error": true,
	}
	if !validLogLevels[c.LogLevel] {
		return fmt.Errorf("invalid log_level: %s (must be debug, info, warn, or error)", c.LogLevel)
	}

	// Validate MTU
	if c.MTU < 576 || c.MTU > 9000 {
		return fmt.Errorf("invalid MTU: %d (must be between 576 and 9000)", c.MTU)
	}

	// Validate buffer size
	if c.BufferSize < 1024 {
		return fmt.Errorf("invalid buffer_size: %d (must be at least 1024)", c.BufferSize)
	}

	// Validate workers
	if c.Workers < 1 || c.Workers > 32 {
		return fmt.Errorf("invalid workers: %d (must be between 1 and 32)", c.Workers)
	}

	return nil
}

// SaveConfig saves the configuration to a YAML file
func (c *Config) SaveConfig(filename string) error {
	data, err := yaml.Marshal(c)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	return nil
}
