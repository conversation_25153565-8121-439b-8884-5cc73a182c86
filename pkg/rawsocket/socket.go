//go:build windows

package rawsocket

import (
	"context"
	"fmt"
	"net"
	"sync"
	"unsafe"

	"golang.org/x/sys/windows"
	"tailscale-nat46/pkg/logger"
)

// RawSocket represents a raw socket for IPv6 communication
type RawSocket struct {
	logger *logger.Logger

	// Socket handles
	sendSocket windows.Handle
	recvSocket windows.Handle

	// Configuration
	localIPv6 net.IP

	// Packet channels
	inbound  chan []byte
	outbound chan []byte

	// Control
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// State
	running bool
	mu      sync.RWMutex
}

// Config represents the configuration for raw socket
type Config struct {
	LocalIPv6 string
	Logger    *logger.Logger
}

// Windows socket constants
const (
	AF_INET6       = 23
	SOCK_RAW       = 3
	IPPROTO_IPV6   = 41
	IPPROTO_TCP    = 6
	IPPROTO_UDP    = 17
	IPPROTO_ICMPV6 = 58
	IPV6_HDRINCL   = 36
)

// NewRawSocket creates a new raw socket
func NewRawSocket(cfg *Config) (*RawSocket, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	if cfg.Logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	// Parse local IPv6 address
	localIPv6 := net.ParseIP(cfg.LocalIPv6)
	if localIPv6 == nil {
		return nil, fmt.Errorf("invalid local IPv6 address: %s", cfg.LocalIPv6)
	}

	ctx, cancel := context.WithCancel(context.Background())

	rs := &RawSocket{
		logger:    cfg.Logger,
		localIPv6: localIPv6,
		inbound:   make(chan []byte, 256),
		outbound:  make(chan []byte, 256),
		ctx:       ctx,
		cancel:    cancel,
	}

	cfg.Logger.Info("Raw socket initialized for IPv6: %s", cfg.LocalIPv6)

	return rs, nil
}

// Start starts the raw socket
func (rs *RawSocket) Start() error {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	if rs.running {
		return fmt.Errorf("raw socket is already running")
	}

	rs.logger.Info("Starting raw socket for IPv6 packet injection")

	// Create raw socket for IPv6 sending
	sendSocket, err := windows.Socket(AF_INET6, SOCK_RAW, IPPROTO_IPV6)
	if err != nil {
		return fmt.Errorf("failed to create send socket: %w", err)
	}
	rs.sendSocket = sendSocket

	// Create raw socket for IPv6 receiving
	recvSocket, err := windows.Socket(AF_INET6, SOCK_RAW, IPPROTO_IPV6)
	if err != nil {
		windows.Closesocket(sendSocket)
		return fmt.Errorf("failed to create receive socket: %w", err)
	}
	rs.recvSocket = recvSocket

	// Set socket options
	if err := rs.configureSendSocket(); err != nil {
		windows.Closesocket(sendSocket)
		windows.Closesocket(recvSocket)
		return fmt.Errorf("failed to configure send socket: %w", err)
	}

	// Configure receive socket
	if err := rs.configureReceiveSocket(); err != nil {
		windows.Closesocket(sendSocket)
		windows.Closesocket(recvSocket)
		return fmt.Errorf("failed to configure receive socket: %w", err)
	}

	// Start packet processing goroutines
	rs.wg.Add(2)
	go rs.sendLoop()
	go rs.receiveLoop()

	rs.running = true
	rs.logger.Info("Raw socket started successfully")

	return nil
}

// Stop stops the raw socket
func (rs *RawSocket) Stop() error {
	rs.mu.Lock()
	defer rs.mu.Unlock()

	if !rs.running {
		return nil
	}

	rs.logger.Info("Stopping raw socket")

	// Cancel context to stop goroutines
	rs.cancel()

	// Wait for goroutines to finish
	rs.wg.Wait()

	// Close sockets
	if rs.sendSocket != 0 {
		windows.Closesocket(rs.sendSocket)
		rs.sendSocket = 0
	}
	if rs.recvSocket != 0 {
		windows.Closesocket(rs.recvSocket)
		rs.recvSocket = 0
	}

	// Close channels
	close(rs.inbound)
	close(rs.outbound)

	rs.running = false
	rs.logger.Info("Raw socket stopped")

	return nil
}

// configureSendSocket configures the send socket options
func (rs *RawSocket) configureSendSocket() error {
	// Try to enable IPv6 header include (may not be supported on Windows)
	hdrincl := int32(1)
	err := windows.Setsockopt(rs.sendSocket, windows.IPPROTO_IPV6, IPV6_HDRINCL,
		(*byte)(unsafe.Pointer(&hdrincl)), int32(unsafe.Sizeof(hdrincl)))
	if err != nil {
		rs.logger.Debug("IPV6_HDRINCL not supported on this system (normal for Windows): %v", err)
		// This is normal on Windows, continue without it
	} else {
		rs.logger.Debug("IPV6_HDRINCL enabled successfully")
	}

	return nil
}

// configureReceiveSocket configures the receive socket options
func (rs *RawSocket) configureReceiveSocket() error {
	// Set receive timeout
	timeout := int32(1000) // 1 second in milliseconds
	err := windows.Setsockopt(rs.recvSocket, windows.SOL_SOCKET, windows.SO_RCVTIMEO,
		(*byte)(unsafe.Pointer(&timeout)), int32(unsafe.Sizeof(timeout)))
	if err != nil {
		rs.logger.Warn("Failed to set receive timeout: %v", err)
	}

	// Bind to local IPv6 address
	addr := &windows.SockaddrInet6{
		Port: 0,
		Addr: [16]byte{},
	}
	copy(addr.Addr[:], rs.localIPv6.To16())

	err = windows.Bind(rs.recvSocket, addr)
	if err != nil {
		rs.logger.Warn("Failed to bind receive socket: %v", err)
		// Continue anyway
	}

	return nil
}

// SendPacket sends an IPv6 packet
func (rs *RawSocket) SendPacket(packet []byte) error {
	if !rs.isRunning() {
		return fmt.Errorf("raw socket is not running")
	}

	select {
	case rs.outbound <- packet:
		return nil
	case <-rs.ctx.Done():
		return fmt.Errorf("raw socket is shutting down")
	default:
		return fmt.Errorf("send queue is full")
	}
}

// ReceivePacket receives an IPv6 packet (blocking)
func (rs *RawSocket) ReceivePacket() ([]byte, error) {
	select {
	case packet := <-rs.inbound:
		return packet, nil
	case <-rs.ctx.Done():
		return nil, fmt.Errorf("raw socket is shutting down")
	}
}

// sendLoop handles outbound packet sending
func (rs *RawSocket) sendLoop() {
	defer rs.wg.Done()
	rs.logger.Debug("Send loop started")

	for {
		select {
		case <-rs.ctx.Done():
			rs.logger.Debug("Send loop stopping")
			return
		case packet := <-rs.outbound:
			if err := rs.sendIPv6Packet(packet); err != nil {
				rs.logger.Error("Failed to send IPv6 packet: %v", err)
			}
		}
	}
}

// receiveLoop handles inbound packet reception
func (rs *RawSocket) receiveLoop() {
	defer rs.wg.Done()
	rs.logger.Debug("Receive loop started")

	buffer := make([]byte, 65536) // 64KB buffer

	for {
		select {
		case <-rs.ctx.Done():
			rs.logger.Debug("Receive loop stopping")
			return
		default:
			// Try to receive IPv6 packet
			n, err := windows.Read(rs.recvSocket, buffer)
			if err != nil {
				// Check if it's a timeout (normal)
				if errno, ok := err.(windows.Errno); ok && errno == windows.WSAETIMEDOUT {
					continue // Timeout is normal, continue
				}
				if rs.ctx.Err() != nil {
					return // Context cancelled
				}
				rs.logger.Debug("Failed to receive IPv6 packet: %v", err)
				continue
			}

			if n > 0 {
				// Make a copy of received data
				packet := make([]byte, n)
				copy(packet, buffer[:n])

				// Basic validation - check if it's an IPv6 packet
				if len(packet) >= 40 && (packet[0]>>4) == 6 {
					rs.logger.Info("Received IPv6 packet: len=%d", len(packet))

					// Send to inbound channel for processing
					select {
					case rs.inbound <- packet:
					case <-rs.ctx.Done():
						return
					default:
						rs.logger.Warn("Receive queue full, dropping IPv6 packet")
					}
				}
			}
		}
	}
}

// sendIPv6Packet sends an IPv6 packet via raw socket
func (rs *RawSocket) sendIPv6Packet(packet []byte) error {
	if len(packet) < 40 {
		return fmt.Errorf("packet too short for IPv6 header")
	}

	// Extract destination IPv6 address from packet
	dstIP := net.IP(packet[24:40])
	srcIP := net.IP(packet[8:24])

	// Get the next header (protocol) from IPv6 header
	nextHeader := packet[6]

	rs.logger.Info("Sending IPv6 packet: %s -> %s, protocol=%d, len=%d",
		srcIP.String(), dstIP.String(), nextHeader, len(packet))

	// Create destination address structure
	addr := &windows.SockaddrInet6{
		Port: 0,
		Addr: [16]byte{},
	}
	copy(addr.Addr[:], dstIP.To16())

	// Send the packet via raw socket
	err := windows.Sendto(rs.sendSocket, packet, 0, addr)
	if err != nil {
		return fmt.Errorf("failed to send IPv6 packet: %w", err)
	}

	rs.logger.Info("Successfully sent IPv6 packet to %s", dstIP.String())
	return nil
}

// isRunning checks if the raw socket is running
func (rs *RawSocket) isRunning() bool {
	rs.mu.RLock()
	defer rs.mu.RUnlock()
	return rs.running
}
