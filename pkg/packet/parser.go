package packet

import (
	"encoding/binary"
	"fmt"
	"net"
)

// IPv4Header represents an IPv4 header
type IPv4Header struct {
	Version        uint8
	IHL            uint8
	TOS            uint8
	TotalLength    uint16
	Identification uint16
	Flags          uint8
	FragmentOffset uint16
	TTL            uint8
	Protocol       uint8
	Checksum       uint16
	SrcIP          net.IP
	DstIP          net.IP
	Options        []byte
}

// TCPHeader represents a TCP header
type TCPHeader struct {
	SrcPort    uint16
	DstPort    uint16
	SeqNum     uint32
	AckNum     uint32
	DataOffset uint8
	Flags      uint8
	Window     uint16
	Checksum   uint16
	UrgentPtr  uint16
	Options    []byte
}

// Packet represents a parsed network packet
type Packet struct {
	IPv4Header *IPv4Header
	TCPHeader  *TCPHeader
	Payload    []byte
	Raw        []byte
}

// TCP flags
const (
	TCPFlagFIN = 0x01
	TCPFlagSYN = 0x02
	TCPFlagRST = 0x04
	TCPFlagPSH = 0x08
	TCPFlagACK = 0x10
	TCPFlagURG = 0x20
)

// IP protocols
const (
	ProtocolTCP = 6
	ProtocolUDP = 17
)

// ParseIPv4Packet parses an IPv4 packet from raw bytes
func ParseIPv4Packet(data []byte) (*Packet, error) {
	if len(data) < 20 {
		return nil, fmt.Errorf("packet too short for IPv4 header")
	}

	packet := &Packet{Raw: data}

	// Parse IPv4 header
	ipv4Header := &IPv4Header{}
	
	versionIHL := data[0]
	ipv4Header.Version = (versionIHL >> 4) & 0x0F
	ipv4Header.IHL = versionIHL & 0x0F
	
	if ipv4Header.Version != 4 {
		return nil, fmt.Errorf("not an IPv4 packet")
	}
	
	headerLength := int(ipv4Header.IHL) * 4
	if headerLength < 20 || len(data) < headerLength {
		return nil, fmt.Errorf("invalid IPv4 header length")
	}

	ipv4Header.TOS = data[1]
	ipv4Header.TotalLength = binary.BigEndian.Uint16(data[2:4])
	ipv4Header.Identification = binary.BigEndian.Uint16(data[4:6])
	
	flagsFragment := binary.BigEndian.Uint16(data[6:8])
	ipv4Header.Flags = uint8((flagsFragment >> 13) & 0x07)
	ipv4Header.FragmentOffset = flagsFragment & 0x1FFF
	
	ipv4Header.TTL = data[8]
	ipv4Header.Protocol = data[9]
	ipv4Header.Checksum = binary.BigEndian.Uint16(data[10:12])
	
	ipv4Header.SrcIP = net.IP(data[12:16])
	ipv4Header.DstIP = net.IP(data[16:20])
	
	if headerLength > 20 {
		ipv4Header.Options = data[20:headerLength]
	}
	
	packet.IPv4Header = ipv4Header

	// Parse transport layer if it's TCP
	if ipv4Header.Protocol == ProtocolTCP {
		tcpData := data[headerLength:]
		tcpHeader, payload, err := parseTCPHeader(tcpData)
		if err != nil {
			return nil, fmt.Errorf("failed to parse TCP header: %w", err)
		}
		packet.TCPHeader = tcpHeader
		packet.Payload = payload
	} else {
		packet.Payload = data[headerLength:]
	}

	return packet, nil
}

// parseTCPHeader parses a TCP header from raw bytes
func parseTCPHeader(data []byte) (*TCPHeader, []byte, error) {
	if len(data) < 20 {
		return nil, nil, fmt.Errorf("TCP header too short")
	}

	header := &TCPHeader{}
	
	header.SrcPort = binary.BigEndian.Uint16(data[0:2])
	header.DstPort = binary.BigEndian.Uint16(data[2:4])
	header.SeqNum = binary.BigEndian.Uint32(data[4:8])
	header.AckNum = binary.BigEndian.Uint32(data[8:12])
	
	dataOffsetFlags := binary.BigEndian.Uint16(data[12:14])
	header.DataOffset = uint8((dataOffsetFlags >> 12) & 0x0F)
	header.Flags = uint8(dataOffsetFlags & 0x3F)
	
	header.Window = binary.BigEndian.Uint16(data[14:16])
	header.Checksum = binary.BigEndian.Uint16(data[16:18])
	header.UrgentPtr = binary.BigEndian.Uint16(data[18:20])
	
	headerLength := int(header.DataOffset) * 4
	if headerLength < 20 || len(data) < headerLength {
		return nil, nil, fmt.Errorf("invalid TCP header length")
	}
	
	if headerLength > 20 {
		header.Options = data[20:headerLength]
	}
	
	payload := data[headerLength:]
	
	return header, payload, nil
}

// BuildIPv4Packet builds an IPv4 packet with the given parameters
func BuildIPv4Packet(srcIP, dstIP net.IP, protocol uint8, payload []byte) []byte {
	headerLength := 20
	totalLength := headerLength + len(payload)
	
	packet := make([]byte, totalLength)
	
	// IPv4 header
	packet[0] = 0x45 // Version 4, IHL 5
	packet[1] = 0    // TOS
	binary.BigEndian.PutUint16(packet[2:4], uint16(totalLength))
	binary.BigEndian.PutUint16(packet[4:6], 0) // Identification
	binary.BigEndian.PutUint16(packet[6:8], 0) // Flags and Fragment Offset
	packet[8] = 64   // TTL
	packet[9] = protocol
	// Checksum will be calculated later
	copy(packet[12:16], srcIP.To4())
	copy(packet[16:20], dstIP.To4())
	
	// Calculate and set checksum
	checksum := calculateIPv4Checksum(packet[:headerLength])
	binary.BigEndian.PutUint16(packet[10:12], checksum)
	
	// Copy payload
	copy(packet[headerLength:], payload)
	
	return packet
}

// BuildTCPPacket builds a TCP packet with the given parameters
func BuildTCPPacket(srcPort, dstPort uint16, seqNum, ackNum uint32, flags uint8, window uint16, payload []byte) []byte {
	headerLength := 20
	totalLength := headerLength + len(payload)
	
	packet := make([]byte, totalLength)
	
	// TCP header
	binary.BigEndian.PutUint16(packet[0:2], srcPort)
	binary.BigEndian.PutUint16(packet[2:4], dstPort)
	binary.BigEndian.PutUint32(packet[4:8], seqNum)
	binary.BigEndian.PutUint32(packet[8:12], ackNum)
	
	dataOffsetFlags := uint16(5<<12) | uint16(flags) // Data offset = 5 (20 bytes)
	binary.BigEndian.PutUint16(packet[12:14], dataOffsetFlags)
	
	binary.BigEndian.PutUint16(packet[14:16], window)
	// Checksum will be calculated later
	binary.BigEndian.PutUint16(packet[18:20], 0) // Urgent pointer
	
	// Copy payload
	copy(packet[headerLength:], payload)
	
	return packet
}

// calculateIPv4Checksum calculates the IPv4 header checksum
func calculateIPv4Checksum(header []byte) uint16 {
	// Set checksum field to 0
	header[10] = 0
	header[11] = 0
	
	var sum uint32
	
	// Sum all 16-bit words
	for i := 0; i < len(header); i += 2 {
		sum += uint32(binary.BigEndian.Uint16(header[i : i+2]))
	}
	
	// Add carry
	for sum>>16 != 0 {
		sum = (sum & 0xFFFF) + (sum >> 16)
	}
	
	// One's complement
	return uint16(^sum)
}

// IsTargetIP checks if the destination IP is in the target range (10.1.1.x)
func IsTargetIP(ip net.IP) bool {
	if ip == nil || ip.To4() == nil {
		return false
	}
	
	ipv4 := ip.To4()
	return ipv4[0] == 10 && ipv4[1] == 1 && ipv4[2] == 1
}

// GetConnectionKey generates a unique key for a TCP connection
func GetConnectionKey(srcIP net.IP, srcPort uint16, dstIP net.IP, dstPort uint16) string {
	return fmt.Sprintf("%s:%d->%s:%d", srcIP.String(), srcPort, dstIP.String(), dstPort)
}
