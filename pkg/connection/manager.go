package connection

import (
	"context"
	"fmt"
	"io"
	"net"
	"sync"
	"time"

	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/packet"
)

// ConnectionState represents the state of a TCP connection
type ConnectionState int

const (
	StateNew ConnectionState = iota
	StateConnecting
	StateEstablished
	StateClosing
	StateClosed
)

// Connection represents a TCP connection mapping
type Connection struct {
	// Client information
	ClientIP   net.IP
	ClientPort uint16
	
	// Target information
	TargetIP   net.IP
	TargetPort uint16
	
	// IPv6 upstream connection
	UpstreamConn net.Conn
	
	// Connection state
	State     ConnectionState
	CreatedAt time.Time
	LastSeen  time.Time
	
	// TCP sequence tracking
	ClientSeq uint32
	ServerSeq uint32
	ClientAck uint32
	ServerAck uint32
	
	// Channels for communication
	ClientToServer chan []byte
	ServerToClient chan []byte
	
	// Context for cancellation
	ctx    context.Context
	cancel context.CancelFunc
	
	// Mutex for thread safety
	mu sync.RWMutex
}

// Manager manages TCP connections
type Manager struct {
	connections map[string]*Connection
	mappings    map[string]string // IPv4 -> IPv6 mappings
	logger      *logger.Logger
	mu          sync.RWMutex
	
	// Cleanup
	cleanupInterval time.Duration
	connectionTTL   time.Duration
	ctx             context.Context
	cancel          context.CancelFunc
	wg              sync.WaitGroup
}

// NewManager creates a new connection manager
func NewManager(mappings map[string]string, logger *logger.Logger) *Manager {
	ctx, cancel := context.WithCancel(context.Background())
	
	m := &Manager{
		connections:     make(map[string]*Connection),
		mappings:        mappings,
		logger:          logger,
		cleanupInterval: 30 * time.Second,
		connectionTTL:   5 * time.Minute,
		ctx:             ctx,
		cancel:          cancel,
	}
	
	// Start cleanup goroutine
	m.wg.Add(1)
	go m.cleanupLoop()
	
	return m
}

// Stop stops the connection manager
func (m *Manager) Stop() {
	m.cancel()
	m.wg.Wait()
	
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Close all connections
	for _, conn := range m.connections {
		conn.Close()
	}
}

// GetOrCreateConnection gets an existing connection or creates a new one
func (m *Manager) GetOrCreateConnection(pkt *packet.Packet) (*Connection, error) {
	if pkt.IPv4Header == nil || pkt.TCPHeader == nil {
		return nil, fmt.Errorf("invalid packet: missing headers")
	}
	
	key := packet.GetConnectionKey(
		pkt.IPv4Header.SrcIP,
		pkt.TCPHeader.SrcPort,
		pkt.IPv4Header.DstIP,
		pkt.TCPHeader.DstPort,
	)
	
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// Check if connection exists
	if conn, exists := m.connections[key]; exists {
		conn.UpdateLastSeen()
		return conn, nil
	}
	
	// Check if target IP has a mapping
	targetIPv6, exists := m.mappings[pkt.IPv4Header.DstIP.String()]
	if !exists {
		return nil, fmt.Errorf("no IPv6 mapping for %s", pkt.IPv4Header.DstIP.String())
	}
	
	// Create new connection
	conn, err := m.createConnection(pkt, targetIPv6)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection: %w", err)
	}
	
	m.connections[key] = conn
	m.logger.Info("Created new connection: %s -> [%s]:%d", 
		key, targetIPv6, pkt.TCPHeader.DstPort)
	
	return conn, nil
}

// createConnection creates a new connection
func (m *Manager) createConnection(pkt *packet.Packet, targetIPv6 string) (*Connection, error) {
	ctx, cancel := context.WithCancel(m.ctx)
	
	conn := &Connection{
		ClientIP:       pkt.IPv4Header.SrcIP,
		ClientPort:     pkt.TCPHeader.SrcPort,
		TargetIP:       pkt.IPv4Header.DstIP,
		TargetPort:     pkt.TCPHeader.DstPort,
		State:          StateNew,
		CreatedAt:      time.Now(),
		LastSeen:       time.Now(),
		ClientSeq:      pkt.TCPHeader.SeqNum,
		ClientToServer: make(chan []byte, 100),
		ServerToClient: make(chan []byte, 100),
		ctx:            ctx,
		cancel:         cancel,
	}
	
	// Connect to IPv6 target
	targetAddr := fmt.Sprintf("[%s]:%d", targetIPv6, pkt.TCPHeader.DstPort)
	upstreamConn, err := net.DialTimeout("tcp", targetAddr, 10*time.Second)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to connect to %s: %w", targetAddr, err)
	}
	
	conn.UpstreamConn = upstreamConn
	conn.State = StateEstablished
	
	// Start data forwarding goroutines
	go conn.forwardClientToServer()
	go conn.forwardServerToClient()
	
	return conn, nil
}

// UpdateLastSeen updates the last seen time
func (c *Connection) UpdateLastSeen() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.LastSeen = time.Now()
}

// Close closes the connection
func (c *Connection) Close() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	if c.State == StateClosed {
		return
	}
	
	c.State = StateClosed
	c.cancel()
	
	if c.UpstreamConn != nil {
		c.UpstreamConn.Close()
	}
	
	close(c.ClientToServer)
	close(c.ServerToClient)
}

// SendToServer sends data to the upstream server
func (c *Connection) SendToServer(data []byte) error {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	if c.State != StateEstablished {
		return fmt.Errorf("connection not established")
	}
	
	select {
	case c.ClientToServer <- data:
		return nil
	case <-c.ctx.Done():
		return fmt.Errorf("connection closed")
	default:
		return fmt.Errorf("send buffer full")
	}
}

// ReceiveFromServer receives data from the upstream server
func (c *Connection) ReceiveFromServer() ([]byte, error) {
	select {
	case data := <-c.ServerToClient:
		return data, nil
	case <-c.ctx.Done():
		return nil, fmt.Errorf("connection closed")
	}
}

// forwardClientToServer forwards data from client to server
func (c *Connection) forwardClientToServer() {
	defer c.UpstreamConn.Close()
	
	for {
		select {
		case data := <-c.ClientToServer:
			if len(data) == 0 {
				return
			}
			
			_, err := c.UpstreamConn.Write(data)
			if err != nil {
				return
			}
			
		case <-c.ctx.Done():
			return
		}
	}
}

// forwardServerToClient forwards data from server to client
func (c *Connection) forwardServerToClient() {
	buffer := make([]byte, 65536)
	
	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			// Set read deadline
			c.UpstreamConn.SetReadDeadline(time.Now().Add(1 * time.Second))
			
			n, err := c.UpstreamConn.Read(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue
				}
				if err == io.EOF {
					return
				}
				return
			}
			
			if n > 0 {
				data := make([]byte, n)
				copy(data, buffer[:n])
				
				select {
				case c.ServerToClient <- data:
				case <-c.ctx.Done():
					return
				default:
					// Buffer full, drop packet
				}
			}
		}
	}
}

// cleanupLoop periodically cleans up old connections
func (m *Manager) cleanupLoop() {
	defer m.wg.Done()
	
	ticker := time.NewTicker(m.cleanupInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			m.cleanup()
		case <-m.ctx.Done():
			return
		}
	}
}

// cleanup removes old connections
func (m *Manager) cleanup() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	now := time.Now()
	toDelete := make([]string, 0)
	
	for key, conn := range m.connections {
		conn.mu.RLock()
		if now.Sub(conn.LastSeen) > m.connectionTTL || conn.State == StateClosed {
			toDelete = append(toDelete, key)
		}
		conn.mu.RUnlock()
	}
	
	for _, key := range toDelete {
		if conn, exists := m.connections[key]; exists {
			conn.Close()
			delete(m.connections, key)
			m.logger.Debug("Cleaned up connection: %s", key)
		}
	}
	
	if len(toDelete) > 0 {
		m.logger.Info("Cleaned up %d old connections", len(toDelete))
	}
}
