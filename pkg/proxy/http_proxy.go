package proxy

import (
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"time"

	"tailscale-nat46/pkg/logger"
)

// HTTPProxy represents an HTTP proxy for IPv4 to IPv6 translation
type HTTPProxy struct {
	logger   *logger.Logger
	mappings map[string]string // IPv4 -> IPv6 mappings
	server   *http.Server
}

// NewHTTPProxy creates a new HTTP proxy
func NewHTTPProxy(logger *logger.Logger, mappings map[string]string) *HTTPProxy {
	return &HTTPProxy{
		logger:   logger,
		mappings: mappings,
	}
}

// Start starts the HTTP proxy server
func (hp *HTTPProxy) Start(listenAddr string) error {
	hp.logger.Info("Starting HTTP proxy on %s", listenAddr)

	mux := http.NewServeMux()
	mux.HandleFunc("/", hp.handleRequest)

	hp.server = &http.Server{
		Addr:         listenAddr,
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
	}

	go func() {
		if err := hp.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			hp.logger.Error("HTTP proxy server error: %v", err)
		}
	}()

	hp.logger.Info("HTTP proxy started successfully")
	return nil
}

// Stop stops the HTTP proxy server
func (hp *HTTPProxy) Stop() error {
	if hp.server != nil {
		hp.logger.Info("Stopping HTTP proxy")
		return hp.server.Close()
	}
	return nil
}

// handleRequest handles incoming HTTP requests and forwards them to IPv6
func (hp *HTTPProxy) handleRequest(w http.ResponseWriter, r *http.Request) {
	// Extract the target IPv4 address from the Host header or URL
	host := r.Host
	if host == "" {
		host = r.URL.Host
	}

	// Remove port if present
	if colonIndex := strings.Index(host, ":"); colonIndex != -1 {
		host = host[:colonIndex]
	}

	hp.logger.Info("HTTP request: %s %s (Host: %s)", r.Method, r.URL.Path, host)

	// Find IPv6 mapping
	ipv6Addr, exists := hp.mappings[host]
	if !exists {
		hp.logger.Error("No IPv6 mapping found for %s", host)
		http.Error(w, "No IPv6 mapping found", http.StatusNotFound)
		return
	}

	// Create target URL with IPv6 address
	targetURL := fmt.Sprintf("http://[%s]%s", ipv6Addr, r.URL.RequestURI())
	if r.URL.Port() != "" {
		targetURL = fmt.Sprintf("http://[%s]:%s%s", ipv6Addr, r.URL.Port(), r.URL.RequestURI())
	}

	hp.logger.Info("Forwarding to IPv6: %s -> %s", host, targetURL)

	// Create new request to IPv6 target
	proxyReq, err := http.NewRequest(r.Method, targetURL, r.Body)
	if err != nil {
		hp.logger.Error("Failed to create proxy request: %v", err)
		http.Error(w, "Failed to create proxy request", http.StatusInternalServerError)
		return
	}

	// Copy headers
	for name, values := range r.Header {
		for _, value := range values {
			proxyReq.Header.Add(name, value)
		}
	}

	// Update Host header to IPv6 address
	proxyReq.Host = fmt.Sprintf("[%s]", ipv6Addr)
	if r.URL.Port() != "" {
		proxyReq.Host = fmt.Sprintf("[%s]:%s", ipv6Addr, r.URL.Port())
	}

	// Create HTTP client with IPv6 support
	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			DialContext: (&net.Dialer{
				Timeout: 10 * time.Second,
			}).DialContext,
		},
	}

	// Send request to IPv6 target
	resp, err := client.Do(proxyReq)
	if err != nil {
		hp.logger.Error("Failed to forward request to IPv6: %v", err)
		http.Error(w, "Failed to forward request", http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	hp.logger.Info("Received response from IPv6: %d %s", resp.StatusCode, resp.Status)

	// Copy response headers
	for name, values := range resp.Header {
		for _, value := range values {
			w.Header().Add(name, value)
		}
	}

	// Set status code
	w.WriteHeader(resp.StatusCode)

	// Copy response body
	written, err := io.Copy(w, resp.Body)
	if err != nil {
		hp.logger.Error("Failed to copy response body: %v", err)
		return
	}

	hp.logger.Info("Successfully forwarded response: %d bytes", written)
}
