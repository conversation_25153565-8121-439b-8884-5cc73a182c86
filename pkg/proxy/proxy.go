//go:build windows

package proxy

import (
	"context"
	"fmt"
	"io"
	"net"
	"sync"
	"time"

	"tailscale-nat46/internal/stats"
	"tailscale-nat46/pkg/config"
	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/wintun"
)

// Proxy represents the main proxy instance
type Proxy struct {
	config *config.Config
	logger *logger.Logger
	stats  *stats.Stats

	// Components
	wintun *wintun.Interface

	// Port listeners
	tcpListeners map[int]net.Listener
	udpConns     map[int]*net.UDPConn

	// Control channels
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// State
	running bool
	mu      sync.RWMutex
}

// NewProxy creates a new proxy instance
func NewProxy(cfg *config.Config, log *logger.Logger) (*Proxy, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if log == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	log.Info("Creating proxy instance")

	// Create Wintun interface configuration
	wintunCfg := &wintun.Config{
		Name:    cfg.InterfaceName,
		LocalIP: cfg.LocalIPv4,
		MTU:     cfg.MTU,
		Logger:  log,
	}

	// Create Wintun interface
	wintunIface, err := wintun.NewInterface(wintunCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create Wintun interface: %w", err)
	}

	p := &Proxy{
		config:       cfg,
		logger:       log,
		stats:        stats.New(),
		wintun:       wintunIface,
		tcpListeners: make(map[int]net.Listener),
		udpConns:     make(map[int]*net.UDPConn),
	}

	log.Info("Proxy instance created successfully")
	log.Debug("Configuration: LocalIPv4=%s, PortMappings=%d",
		cfg.LocalIPv4, len(cfg.PortMappings))

	return p, nil
}

// Start starts the proxy
func (p *Proxy) Start(ctx context.Context) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.running {
		return fmt.Errorf("proxy is already running")
	}

	p.ctx, p.cancel = context.WithCancel(ctx)
	p.logger.Info("Starting proxy...")

	// 1. Start Wintun interface
	p.logger.Info("Starting Wintun interface...")
	if err := p.wintun.Start(); err != nil {
		return fmt.Errorf("failed to start Wintun interface: %w", err)
	}

	// 2. Start port listeners
	p.logger.Info("Starting port listeners...")
	if err := p.startPortListeners(); err != nil {
		p.wintun.Stop()
		return fmt.Errorf("failed to start port listeners: %w", err)
	}

	p.running = true
	p.logger.Info("Proxy started successfully")

	return nil
}

// startPortListeners starts all configured port listeners
func (p *Proxy) startPortListeners() error {
	for _, mapping := range p.config.PortMappings {
		if mapping.Protocol == "tcp" || mapping.Protocol == "both" {
			if err := p.startTCPListener(mapping.ListenPort); err != nil {
				return fmt.Errorf("failed to start TCP listener on port %d: %w", mapping.ListenPort, err)
			}
		}

		if mapping.Protocol == "udp" || mapping.Protocol == "both" {
			if err := p.startUDPListener(mapping.ListenPort); err != nil {
				return fmt.Errorf("failed to start UDP listener on port %d: %w", mapping.ListenPort, err)
			}
		}
	}
	return nil
}

// startTCPListener starts a TCP listener on the specified port
func (p *Proxy) startTCPListener(port int) error {
	addr := fmt.Sprintf(":%d", port)
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		return err
	}

	p.tcpListeners[port] = listener
	p.logger.Info("Started TCP listener on port %d", port)

	// Start accepting connections
	p.wg.Add(1)
	go p.handleTCPConnections(listener, port)

	return nil
}

// startUDPListener starts a UDP listener on the specified port
func (p *Proxy) startUDPListener(port int) error {
	addr := fmt.Sprintf(":%d", port)
	udpAddr, err := net.ResolveUDPAddr("udp", addr)
	if err != nil {
		return err
	}

	conn, err := net.ListenUDP("udp", udpAddr)
	if err != nil {
		return err
	}

	p.udpConns[port] = conn
	p.logger.Info("Started UDP listener on port %d", port)

	// Start handling UDP packets
	p.wg.Add(1)
	go p.handleUDPPackets(conn, port)

	return nil
}

// stopListeners stops all port listeners
func (p *Proxy) stopListeners() {
	// Stop TCP listeners
	for port, listener := range p.tcpListeners {
		p.logger.Info("Stopping TCP listener on port %d", port)
		if err := listener.Close(); err != nil {
			p.logger.Error("Failed to close TCP listener on port %d: %v", port, err)
		}
	}

	// Stop UDP connections
	for port, conn := range p.udpConns {
		p.logger.Info("Stopping UDP listener on port %d", port)
		if err := conn.Close(); err != nil {
			p.logger.Error("Failed to close UDP connection on port %d: %v", port, err)
		}
	}

	// Clear the maps
	p.tcpListeners = make(map[int]net.Listener)
	p.udpConns = make(map[int]*net.UDPConn)
}

// Stop stops the proxy
func (p *Proxy) Stop() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if !p.running {
		return nil
	}

	p.logger.Info("Stopping proxy...")

	// Cancel context to signal shutdown
	if p.cancel != nil {
		p.cancel()
	}

	// Stop all listeners
	p.stopListeners()

	// Wait for all goroutines to finish
	p.wg.Wait()

	// Stop Wintun interface
	if err := p.wintun.Stop(); err != nil {
		p.logger.Error("Failed to stop Wintun interface: %v", err)
	}

	p.running = false
	p.logger.Info("Proxy stopped successfully")

	return nil
}

// IsRunning returns whether the proxy is currently running
func (p *Proxy) IsRunning() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.running
}

// GetStats returns the current statistics
func (p *Proxy) GetStats() *stats.Stats {
	return p.stats
}

// GetConfig returns the current configuration
func (p *Proxy) GetConfig() *config.Config {
	return p.config
}

// handleTCPConnections handles incoming TCP connections
func (p *Proxy) handleTCPConnections(listener net.Listener, port int) {
	defer p.wg.Done()
	p.logger.Info("Starting TCP connection handler for port %d", port)

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("TCP connection handler for port %d stopping", port)
			return
		default:
			// Set a deadline for Accept to make it non-blocking
			if tcpListener, ok := listener.(*net.TCPListener); ok {
				tcpListener.SetDeadline(time.Now().Add(1 * time.Second))
			}

			conn, err := listener.Accept()
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue // Timeout, check context again
				}
				if p.ctx.Err() != nil {
					return // Context cancelled
				}
				p.logger.Error("Failed to accept TCP connection on port %d: %v", port, err)
				continue
			}

			// Handle the connection in a separate goroutine
			go p.handleTCPConnection(conn, port)
		}
	}
}

// handleTCPConnection handles a single TCP connection
func (p *Proxy) handleTCPConnection(clientConn net.Conn, port int) {
	defer clientConn.Close()

	clientAddr := clientConn.RemoteAddr().String()
	p.logger.Info("New TCP connection from %s to port %d", clientAddr, port)

	// Connect to the virtual interface
	targetAddr := fmt.Sprintf("%s:%d", p.config.LocalIPv4, port)
	targetConn, err := net.Dial("tcp", targetAddr)
	if err != nil {
		p.logger.Error("Failed to connect to target %s: %v", targetAddr, err)
		return
	}
	defer targetConn.Close()

	p.logger.Info("Established connection: %s -> %s", clientAddr, targetAddr)

	// Start bidirectional data forwarding
	done := make(chan struct{}, 2)

	// Forward client -> target
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(targetConn, clientConn)
		if err != nil {
			p.logger.Debug("Client->Target copy finished with error: %v", err)
		} else {
			p.logger.Debug("Client->Target copy finished: %d bytes", written)
		}
	}()

	// Forward target -> client
	go func() {
		defer func() { done <- struct{}{} }()
		written, err := io.Copy(clientConn, targetConn)
		if err != nil {
			p.logger.Debug("Target->Client copy finished with error: %v", err)
		} else {
			p.logger.Debug("Target->Client copy finished: %d bytes", written)
		}
	}()

	// Wait for either direction to finish
	<-done
	p.logger.Info("TCP connection closed: %s -> %s", clientAddr, targetAddr)
}

// handleUDPPackets handles UDP packets for a specific port
func (p *Proxy) handleUDPPackets(conn *net.UDPConn, port int) {
	defer p.wg.Done()
	p.logger.Info("Starting UDP packet handler for port %d", port)

	// Buffer for reading UDP packets
	buffer := make([]byte, 65536)

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("UDP packet handler for port %d stopping", port)
			return
		default:
			// Set read deadline to make it non-blocking
			conn.SetReadDeadline(time.Now().Add(1 * time.Second))

			n, clientAddr, err := conn.ReadFromUDP(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue // Timeout, check context again
				}
				if p.ctx.Err() != nil {
					return // Context cancelled
				}
				p.logger.Error("Failed to read UDP packet on port %d: %v", port, err)
				continue
			}

			// Handle the packet in a separate goroutine
			go p.handleUDPPacket(buffer[:n], clientAddr, port)
		}
	}
}

// handleUDPPacket handles a single UDP packet
func (p *Proxy) handleUDPPacket(data []byte, clientAddr *net.UDPAddr, port int) {
	p.logger.Debug("Received UDP packet from %s to port %d, size: %d", clientAddr.String(), port, len(data))

	// Forward to the virtual interface
	targetAddr := fmt.Sprintf("%s:%d", p.config.LocalIPv4, port)
	targetConn, err := net.Dial("udp", targetAddr)
	if err != nil {
		p.logger.Error("Failed to connect to UDP target %s: %v", targetAddr, err)
		return
	}
	defer targetConn.Close()

	// Send data to target
	_, err = targetConn.Write(data)
	if err != nil {
		p.logger.Error("Failed to send UDP data to target %s: %v", targetAddr, err)
		return
	}

	// Read response from target
	targetConn.SetReadDeadline(time.Now().Add(5 * time.Second))
	responseBuffer := make([]byte, 65536)
	n, err := targetConn.Read(responseBuffer)
	if err != nil {
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			p.logger.Debug("UDP response timeout for %s", clientAddr.String())
			return
		}
		p.logger.Error("Failed to read UDP response from target %s: %v", targetAddr, err)
		return
	}

	// Send response back to client
	responseConn, err := net.Dial("udp", clientAddr.String())
	if err != nil {
		p.logger.Error("Failed to connect back to UDP client %s: %v", clientAddr.String(), err)
		return
	}
	defer responseConn.Close()

	_, err = responseConn.Write(responseBuffer[:n])
	if err != nil {
		p.logger.Error("Failed to send UDP response to client %s: %v", clientAddr.String(), err)
		return
	}

	p.logger.Debug("Successfully forwarded UDP packet: %s -> %s -> %s", clientAddr.String(), targetAddr, clientAddr.String())
}
