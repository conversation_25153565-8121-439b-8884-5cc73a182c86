//go:build windows

package proxy

import (
	"context"
	"fmt"
	"sync"
	"time"

	"tailscale-nat46/internal/stats"
	"tailscale-nat46/pkg/config"
	"tailscale-nat46/pkg/connection"
	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/packet"
	"tailscale-nat46/pkg/wintun"
)

// Proxy represents the main proxy instance
type Proxy struct {
	config *config.Config
	logger *logger.Logger
	stats  *stats.Stats

	// Components
	wintun  *wintun.Interface
	connMgr *connection.Manager

	// Control channels
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// State
	running bool
	mu      sync.RWMutex
}

// NewProxy creates a new proxy instance
func NewProxy(cfg *config.Config, log *logger.Logger) (*Proxy, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if log == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	log.Info("Creating proxy instance")

	// Create Wintun interface configuration
	wintunCfg := &wintun.Config{
		Name:    cfg.InterfaceName,
		LocalIP: cfg.LocalIPv4,
		MTU:     cfg.MTU,
		Logger:  log,
	}

	// Create Wintun interface
	wintunIface, err := wintun.NewInterface(wintunCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create Wintun interface: %w", err)
	}

	// Create connection manager
	connMgr := connection.NewManager(cfg.Mappings, log)

	p := &Proxy{
		config:  cfg,
		logger:  log,
		stats:   stats.New(),
		wintun:  wintunIface,
		connMgr: connMgr,
	}

	log.Info("Proxy instance created successfully")
	log.Debug("Configuration: LocalIPv4=%s, Mappings=%d",
		cfg.LocalIPv4, len(cfg.Mappings))

	return p, nil
}

// Start starts the proxy
func (p *Proxy) Start(ctx context.Context) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.running {
		return fmt.Errorf("proxy is already running")
	}

	p.ctx, p.cancel = context.WithCancel(ctx)
	p.logger.Info("Starting proxy...")

	// 1. Start Wintun interface
	p.logger.Info("Starting Wintun interface...")
	if err := p.wintun.Start(); err != nil {
		return fmt.Errorf("failed to start Wintun interface: %w", err)
	}

	// 2. Start packet processing
	p.logger.Info("Starting packet processing...")
	p.wg.Add(2)
	go p.packetProcessor()
	go p.responseProcessor()

	p.running = true
	p.logger.Info("Proxy started successfully")

	return nil
}

// packetProcessor processes incoming packets from Wintun
func (p *Proxy) packetProcessor() {
	defer p.wg.Done()
	p.logger.Info("Starting packet processor")

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("Packet processor stopping")
			return
		default:
			// Read packet from Wintun interface
			packetData, err := p.wintun.ReadPacket()
			if err != nil {
				if p.ctx.Err() != nil {
					return // Context cancelled
				}
				p.logger.Debug("Failed to read packet from Wintun: %v", err)
				continue
			}

			// Process the packet
			go p.handlePacket(packetData)
		}
	}
}

// responseProcessor processes responses from IPv6 connections
func (p *Proxy) responseProcessor() {
	defer p.wg.Done()
	p.logger.Info("Starting response processor")

	ticker := time.NewTicker(10 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("Response processor stopping")
			return
		case <-ticker.C:
			// This will be implemented to handle responses from connections
			// For now, we'll rely on the connection manager to handle responses
		}
	}
}

// Stop stops the proxy
func (p *Proxy) Stop() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if !p.running {
		return nil
	}

	p.logger.Info("Stopping proxy...")

	// Cancel context to signal shutdown
	if p.cancel != nil {
		p.cancel()
	}

	// Stop connection manager
	if p.connMgr != nil {
		p.connMgr.Stop()
	}

	// Wait for all goroutines to finish
	p.wg.Wait()

	// Stop Wintun interface
	if err := p.wintun.Stop(); err != nil {
		p.logger.Error("Failed to stop Wintun interface: %v", err)
	}

	p.running = false
	p.logger.Info("Proxy stopped successfully")

	return nil
}

// IsRunning returns whether the proxy is currently running
func (p *Proxy) IsRunning() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.running
}

// GetStats returns the current statistics
func (p *Proxy) GetStats() *stats.Stats {
	return p.stats
}

// GetConfig returns the current configuration
func (p *Proxy) GetConfig() *config.Config {
	return p.config
}

// handlePacket processes a single packet from Wintun
func (p *Proxy) handlePacket(data []byte) {
	// Parse the packet
	pkt, err := packet.ParseIPv4Packet(data)
	if err != nil {
		p.logger.Debug("Failed to parse packet: %v", err)
		return
	}

	// Check if this is a target IP (10.1.1.x)
	if !packet.IsTargetIP(pkt.IPv4Header.DstIP) {
		return
	}

	// Only handle TCP packets for now
	if pkt.IPv4Header.Protocol != packet.ProtocolTCP {
		p.logger.Debug("Ignoring non-TCP packet to %s", pkt.IPv4Header.DstIP.String())
		return
	}

	p.logger.Info("Processing TCP packet: %s:%d -> %s:%d",
		pkt.IPv4Header.SrcIP.String(), pkt.TCPHeader.SrcPort,
		pkt.IPv4Header.DstIP.String(), pkt.TCPHeader.DstPort)

	// Handle TCP connection
	if err := p.handleTCPPacket(pkt); err != nil {
		p.logger.Error("Failed to handle TCP packet: %v", err)
	}
}

// handleTCPPacket handles a TCP packet
func (p *Proxy) handleTCPPacket(pkt *packet.Packet) error {
	// Get or create connection
	conn, err := p.connMgr.GetOrCreateConnection(pkt)
	if err != nil {
		return fmt.Errorf("failed to get connection: %w", err)
	}

	// Handle different TCP flags
	if pkt.TCPHeader.Flags&packet.TCPFlagSYN != 0 {
		p.logger.Debug("SYN packet for connection")
		// For SYN packets, the connection is already established
		// Send SYN-ACK response
		return p.sendSynAckResponse(pkt, conn)
	}

	if pkt.TCPHeader.Flags&packet.TCPFlagACK != 0 && len(pkt.Payload) > 0 {
		p.logger.Debug("Data packet: %d bytes", len(pkt.Payload))
		// Send data to upstream server
		return conn.SendToServer(pkt.Payload)
	}

	if pkt.TCPHeader.Flags&packet.TCPFlagFIN != 0 {
		p.logger.Debug("FIN packet for connection")
		// Handle connection close
		conn.Close()
	}

	return nil
}

// sendSynAckResponse sends a SYN-ACK response for a new connection
func (p *Proxy) sendSynAckResponse(pkt *packet.Packet, conn *connection.Connection) error {
	// Build SYN-ACK response
	tcpPayload := packet.BuildTCPPacket(
		pkt.TCPHeader.DstPort,               // src port
		pkt.TCPHeader.SrcPort,               // dst port
		12345,                               // seq num (should be random)
		pkt.TCPHeader.SeqNum+1,              // ack num
		packet.TCPFlagSYN|packet.TCPFlagACK, // flags
		65535,                               // window
		nil,                                 // no payload
	)

	// Build IPv4 response packet
	responsePacket := packet.BuildIPv4Packet(
		pkt.IPv4Header.DstIP, // src IP
		pkt.IPv4Header.SrcIP, // dst IP
		packet.ProtocolTCP,   // protocol
		tcpPayload,           // payload
	)

	// Send response via Wintun
	if err := p.wintun.WritePacket(responsePacket); err != nil {
		return fmt.Errorf("failed to send SYN-ACK: %w", err)
	}

	p.logger.Debug("Sent SYN-ACK response")
	return nil
}
