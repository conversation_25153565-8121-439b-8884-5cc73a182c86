//go:build windows

package proxy

import (
	"context"
	"fmt"
	"net"
	"os/exec"
	"sync"
	"time"

	"tailscale-nat46/internal/stats"
	"tailscale-nat46/pkg/config"
	"tailscale-nat46/pkg/logger"
	"tailscale-nat46/pkg/netstack"
	"tailscale-nat46/pkg/rawsocket"
	"tailscale-nat46/pkg/routing"
	"tailscale-nat46/pkg/translator"
	"tailscale-nat46/pkg/wintun"
)

// Proxy represents the main proxy instance
type Proxy struct {
	config *config.Config
	logger *logger.Logger
	stats  *stats.Stats

	// Components
	wintun     *wintun.Interface
	routing    *routing.Manager
	netstack   *netstack.Stack
	translator *translator.Translator
	rawsocket  *rawsocket.RawSocket
	httpProxy  *HTTPProxy

	// Control channels
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup

	// State
	running bool
	mu      sync.RWMutex
}

// NewProxy creates a new proxy instance
func NewProxy(cfg *config.Config, log *logger.Logger) (*Proxy, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if log == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	log.Info("Creating proxy instance")

	// Create routing manager
	routingMgr := routing.NewManager(log)

	// Create Wintun interface configuration
	wintunCfg := &wintun.Config{
		Name:    cfg.InterfaceName,
		LocalIP: cfg.LocalIPv4,
		MTU:     cfg.MTU,
		Logger:  log,
	}

	// Create Wintun interface
	wintunIface, err := wintun.NewInterface(wintunCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create Wintun interface: %w", err)
	}

	// Create network stack
	netstackCfg := &netstack.Config{
		LocalIPv4: cfg.LocalIPv4,
		LocalIPv6: cfg.LocalIPv6,
		Logger:    log,
	}

	netStack, err := netstack.NewStack(netstackCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create network stack: %w", err)
	}

	// Create translator
	translatorCfg := &translator.Config{
		LocalIPv4: cfg.LocalIPv4,
		LocalIPv6: cfg.LocalIPv6,
		Mappings:  cfg.Mappings,
		Logger:    log,
		NetStack:  netStack,
	}

	trans, err := translator.NewTranslator(translatorCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create translator: %w", err)
	}

	// Create raw socket
	rawsocketCfg := &rawsocket.Config{
		LocalIPv6: cfg.LocalIPv6,
		Logger:    log,
	}

	rawSocket, err := rawsocket.NewRawSocket(rawsocketCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create raw socket: %w", err)
	}

	// Create HTTP proxy
	httpProxy := NewHTTPProxy(log, cfg.Mappings)

	p := &Proxy{
		config:     cfg,
		logger:     log,
		stats:      stats.New(),
		wintun:     wintunIface,
		routing:    routingMgr,
		netstack:   netStack,
		translator: trans,
		rawsocket:  rawSocket,
		httpProxy:  httpProxy,
	}

	log.Info("Proxy instance created successfully")
	log.Debug("Configuration: LocalIPv4=%s, LocalIPv6=%s, Mappings=%d",
		cfg.LocalIPv4, cfg.LocalIPv6, len(cfg.Mappings))

	return p, nil
}

// Start starts the proxy
func (p *Proxy) Start(ctx context.Context) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.running {
		return fmt.Errorf("proxy is already running")
	}

	p.ctx, p.cancel = context.WithCancel(ctx)
	p.logger.Info("Starting proxy...")

	// 1. Start network stack
	p.logger.Info("Starting network stack...")
	if err := p.netstack.Start(); err != nil {
		return fmt.Errorf("failed to start network stack: %w", err)
	}

	// 2. Start translator
	p.logger.Info("Starting translator...")
	if err := p.translator.Start(); err != nil {
		p.netstack.Stop()
		return fmt.Errorf("failed to start translator: %w", err)
	}

	// 3. Start raw socket
	p.logger.Info("Starting raw socket...")
	if err := p.rawsocket.Start(); err != nil {
		p.translator.Stop()
		p.netstack.Stop()
		return fmt.Errorf("failed to start raw socket: %w", err)
	}

	// 4. Start Wintun interface
	p.logger.Info("Starting Wintun interface...")
	if err := p.wintun.Start(); err != nil {
		p.rawsocket.Stop()
		p.translator.Stop()
		p.netstack.Stop()
		return fmt.Errorf("failed to start Wintun interface: %w", err)
	}

	// 5. Start HTTP proxy
	p.logger.Info("Starting HTTP proxy...")
	if err := p.httpProxy.Start(":8080"); err != nil {
		p.stopComponents()
		return fmt.Errorf("failed to start HTTP proxy: %w", err)
	}

	// 6. Configure routing for mapped IPs
	p.logger.Info("Configuring routes for mapped IPs...")
	if err := p.configureRoutes(); err != nil {
		p.stopComponents()
		return fmt.Errorf("failed to configure routes: %w", err)
	}

	// 7. Start packet processing workers
	p.logger.Info("Starting packet processing workers...")
	p.wg.Add(3)
	go p.wintunToIPv6Processor()
	go p.ipv6ToWintunProcessor()
	go p.sessionCleanupProcessor()

	p.running = true
	p.logger.Info("Proxy started successfully")

	return nil
}

// Stop stops the proxy
func (p *Proxy) Stop() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if !p.running {
		return nil
	}

	p.logger.Info("Stopping proxy...")

	// Cancel context to signal shutdown
	if p.cancel != nil {
		p.cancel()
	}

	// Wait for all goroutines to finish
	p.wg.Wait()

	// Cleanup components
	p.stopComponents()

	p.running = false
	p.logger.Info("Proxy stopped successfully")

	return nil
}

// IsRunning returns whether the proxy is currently running
func (p *Proxy) IsRunning() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.running
}

// GetStats returns the current statistics
func (p *Proxy) GetStats() *stats.Stats {
	return p.stats
}

// GetConfig returns the current configuration
func (p *Proxy) GetConfig() *config.Config {
	return p.config
}

// stopComponents stops all components in reverse order
func (p *Proxy) stopComponents() {
	// 1. Remove routing rules
	p.logger.Info("Removing routing rules...")
	if err := p.routing.RemoveAllRoutes(); err != nil {
		p.logger.Error("Failed to remove routes: %v", err)
	}

	// 2. Stop HTTP proxy
	p.logger.Info("Stopping HTTP proxy...")
	if err := p.httpProxy.Stop(); err != nil {
		p.logger.Error("Failed to stop HTTP proxy: %v", err)
	}

	// 3. Stop Wintun interface
	p.logger.Info("Stopping Wintun interface...")
	if err := p.wintun.Stop(); err != nil {
		p.logger.Error("Failed to stop Wintun interface: %v", err)
	}

	// 4. Stop raw socket
	p.logger.Info("Stopping raw socket...")
	if err := p.rawsocket.Stop(); err != nil {
		p.logger.Error("Failed to stop raw socket: %v", err)
	}

	// 5. Stop translator
	p.logger.Info("Stopping translator...")
	if err := p.translator.Stop(); err != nil {
		p.logger.Error("Failed to stop translator: %v", err)
	}

	// 6. Stop network stack
	p.logger.Info("Stopping network stack...")
	if err := p.netstack.Stop(); err != nil {
		p.logger.Error("Failed to stop network stack: %v", err)
	}
}

// configureRoutes configures routing rules for mapped IP addresses
func (p *Proxy) configureRoutes() error {
	// Get the local IP address to use as gateway
	localIP := net.ParseIP(p.config.LocalIPv4)
	if localIP == nil {
		return fmt.Errorf("invalid local IPv4 address: %s", p.config.LocalIPv4)
	}

	p.logger.Info("Configuring routes for mapped IPs via gateway %s", localIP.String())

	// Add routes for each mapped IP using simple route command
	for ipv4Str := range p.config.Mappings {
		destIP := net.ParseIP(ipv4Str)
		if destIP == nil {
			p.logger.Error("Invalid IPv4 address in mapping: %s", ipv4Str)
			continue
		}

		p.logger.Info("Adding route for %s via %s", ipv4Str, localIP.String())
		if err := p.addRouteCommand(destIP, localIP); err != nil {
			p.logger.Error("Failed to add route for %s: %v", ipv4Str, err)
			// Continue with other routes instead of failing completely
		} else {
			p.logger.Info("Successfully added route: %s -> %s", ipv4Str, localIP.String())
		}
	}

	return nil
}

// addRouteCommand adds a route using the route command
func (p *Proxy) addRouteCommand(destIP, gatewayIP net.IP) error {
	// For HTTP proxy, we need to route to localhost:8080
	// We'll use netsh to add port forwarding
	_ = gatewayIP // unused in this implementation
	cmd := fmt.Sprintf("netsh interface portproxy add v4tov4 listenaddress=%s listenport=80 connectaddress=127.0.0.1 connectport=8080", destIP.String())

	p.logger.Debug("Executing command: %s", cmd)

	// Execute the command
	if err := p.executeCommand("cmd", "/c", cmd); err != nil {
		return fmt.Errorf("failed to execute netsh command: %w", err)
	}

	return nil
}

// executeCommand executes a system command
func (p *Proxy) executeCommand(name string, args ...string) error {
	cmd := exec.Command(name, args...)
	output, err := cmd.CombinedOutput()

	if err != nil {
		p.logger.Error("Command failed: %s %v, output: %s, error: %v", name, args, string(output), err)
		return err
	}

	p.logger.Debug("Command executed successfully: %s %v", name, args)
	if len(output) > 0 {
		p.logger.Debug("Command output: %s", string(output))
	}

	return nil
}

// wintunToIPv6Processor processes packets from Wintun to IPv6
func (p *Proxy) wintunToIPv6Processor() {
	defer p.wg.Done()
	p.logger.Info("Starting Wintun to IPv6 processor")

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("Wintun to IPv6 processor stopping")
			return
		default:
			// Read packet from Wintun interface
			packetData, err := p.wintun.ReadPacket()
			if err != nil {
				if p.ctx.Err() != nil {
					return // Context cancelled
				}
				p.logger.Debug("Failed to read packet from Wintun: %v", err)
				continue
			}

			p.logger.Info("Received packet from Wintun: len=%d", len(packetData))

			// Parse IPv4 packet
			packet, err := p.netstack.ProcessInboundPacket(packetData)
			if err != nil {
				p.logger.Error("Failed to parse packet: %v", err)
				continue
			}

			// Only process IPv4 packets
			if !packet.IsIPv4 {
				p.logger.Debug("Ignoring non-IPv4 packet")
				continue
			}

			// Skip multicast and broadcast addresses
			dstIP := packet.IPv4Header.DstIP
			if dstIP.IsMulticast() || dstIP.Equal(net.IPv4bcast) {
				p.logger.Debug("Ignoring multicast/broadcast packet: %s", dstIP.String())
				continue
			}

			p.logger.Info("Processing IPv4 packet: %s -> %s, protocol=%d",
				packet.IPv4Header.SrcIP.String(),
				packet.IPv4Header.DstIP.String(),
				packet.IPv4Header.Protocol)

			// Translate to IPv6
			ipv6Packet, err := p.translator.TranslateIPv4ToIPv6(packet)
			if err != nil {
				p.logger.Error("Failed to translate packet: %v", err)
				continue
			}

			p.logger.Info("Translated to IPv6 packet: len=%d", len(ipv6Packet))

			// Send via raw socket
			if err := p.rawsocket.SendPacket(ipv6Packet); err != nil {
				p.logger.Error("Failed to send IPv6 packet: %v", err)
				continue
			}

			p.stats.IncrementPacketsTranslated()
			p.logger.Info("Successfully translated and sent IPv4->IPv6 packet")
		}
	}
}

// ipv6ToWintunProcessor processes packets from IPv6 to Wintun
func (p *Proxy) ipv6ToWintunProcessor() {
	defer p.wg.Done()
	p.logger.Info("Starting IPv6 to Wintun processor")

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("IPv6 to Wintun processor stopping")
			return
		default:
			// Read packet from raw socket
			packetData, err := p.rawsocket.ReceivePacket()
			if err != nil {
				if p.ctx.Err() != nil {
					return // Context cancelled
				}
				p.logger.Debug("Failed to read packet from raw socket: %v", err)
				continue
			}

			// Parse IPv6 packet
			packet, err := p.netstack.ProcessInboundPacket(packetData)
			if err != nil {
				p.logger.Debug("Failed to parse IPv6 packet: %v", err)
				continue
			}

			// Only process IPv6 packets
			if !packet.IsIPv6 {
				continue
			}

			// Translate to IPv4
			ipv4Packet, err := p.translator.TranslateIPv6ToIPv4(packet)
			if err != nil {
				p.logger.Debug("Failed to translate IPv6 packet: %v", err)
				continue
			}

			// Send via Wintun interface
			if err := p.wintun.WritePacket(ipv4Packet); err != nil {
				p.logger.Error("Failed to send IPv4 packet: %v", err)
				continue
			}

			p.stats.IncrementPacketsTranslated()
			p.logger.Debug("Translated and sent IPv6->IPv4 packet: len=%d", len(ipv4Packet))
		}
	}
}

// sessionCleanupProcessor periodically cleans up old sessions
func (p *Proxy) sessionCleanupProcessor() {
	defer p.wg.Done()
	p.logger.Info("Starting session cleanup processor")

	ticker := time.NewTicker(30 * time.Second) // Cleanup every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-p.ctx.Done():
			p.logger.Info("Session cleanup processor stopping")
			return
		case <-ticker.C:
			// Clean up sessions older than 5 minutes
			p.translator.CleanupSessions(5 * time.Minute)

			sessionCount := p.translator.GetSessionCount()
			p.logger.Debug("Active sessions: %d", sessionCount)
		}
	}
}
