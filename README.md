# GoProxy - Port Forwarding Proxy for Industrial Networks

GoProxy is a Windows-based port forwarding proxy designed for industrial network environments. It creates a virtual network interface and forwards traffic from specified ports to the virtual interface IP address (************), maintaining the same port numbers.

## Overview

GoProxy is a Windows command-line tool that creates a virtual network interface using Wintun and forwards traffic from configured ports to the virtual interface. Unlike traditional proxies, it performs simple port forwarding without protocol translation, making it ideal for industrial applications that need traffic redirection.

## Key Features

- **Simple Port Forwarding**: Forward TCP/UDP traffic to virtual network interface
- **No Protocol Translation**: Pure port forwarding without IPv4-to-IPv6 conversion
- **Multiple Protocol Support**: TCP, UDP, or both for each port
- **Virtual Network Interface**: Uses Wintun driver for virtual networking
- **Industrial Network Ready**: Designed for industrial environments
- **Windows Native**: Built specifically for Windows 10/11

## Architecture

The tool works by:

1. **Creating a Wintun virtual network interface** named `tailscale-nat46`
2. **Configuring precise routing rules** to intercept specific IPv4 traffic
3. **Processing packets in user-space** using an embedded network stack
4. **Translating IPv4 packets to IPv6** based on configured mappings
5. **Injecting IPv6 packets** into the physical network interface for Tailscale to handle
6. **Handling return traffic** by capturing IPv6 responses and converting them back to IPv4

## Requirements

- Windows 10/11 (64-bit)
- Administrator privileges
- Active Tailscale connection
- Wintun driver (bundled with Tailscale)

## Quick Start

1. Download the latest release
2. Create a configuration file (see Configuration section)
3. Run as Administrator: `tailscale-nat46.exe -config config.yaml`

## Configuration

Create a `config.yaml` file:

```yaml
# Local IPv4 address for the virtual interface
local_ipv4: "************"

# Your Tailscale IPv6 address (get with: tailscale ip -6)
local_ipv6: "fd7a:115c:a1e0:b1a:0:7:a8c0:101"

# IP mappings: IPv4 -> IPv6
mappings:
  "********": "fd7a:115c:a1e0:b1a:0:7:c0a8:601"
  "********": "fd7a:115c:a1e0:b1a:0:7:c0a8:602"

# Logging configuration
log_level: "info"
log_file: "tailscale-nat46.log"
```

## Building

```bash
# Install dependencies
go mod tidy

# Build for Windows
GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -o tailscale-nat46.exe ./cmd/tailscale-nat46

# Or use the build script
./build.sh
```

## License

MIT License - see LICENSE file for details.

## Contributing

Contributions are welcome! Please read CONTRIBUTING.md for guidelines.
