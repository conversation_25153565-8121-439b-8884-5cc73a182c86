#!/bin/bash

# Build script for tailscale-nat46

set -e

APP_NAME="tailscale-nat46"
VERSION=${1:-"1.0.0"}
BUILD_DIR="build"

echo "Building $APP_NAME v$VERSION..."

# Create build directory
mkdir -p $BUILD_DIR

# Set build variables for Windows
export GOOS=windows
export GOARCH=amd64
export CGO_ENABLED=0

# Build flags
LDFLAGS="-s -w -X main.appVersion=$VERSION"

echo "Target: $GOOS/$GOARCH"
echo "Version: $VERSION"
echo ""

# Build the main application
echo "Building main application..."
go build -ldflags="$LDFLAGS" -o "$BUILD_DIR/$APP_NAME.exe" "./cmd/$APP_NAME"

if [ $? -eq 0 ]; then
    echo "✅ Build successful: $BUILD_DIR/$APP_NAME.exe"
    
    # Show file info
    ls -lh "$BUILD_DIR/$APP_NAME.exe"
    
    # Copy example configuration
    echo ""
    echo "Copying example configuration..."
    cp examples/config.yaml "$BUILD_DIR/"
    echo "✅ Example configuration copied to $BUILD_DIR/config.yaml"
    
    echo ""
    echo "Build complete! Files in $BUILD_DIR/:"
    ls -la "$BUILD_DIR/"
    
    echo ""
    echo "To run:"
    echo "  1. Copy $BUILD_DIR/$APP_NAME.exe and $BUILD_DIR/config.yaml to your Windows machine"
    echo "  2. Edit config.yaml with your Tailscale IPv6 addresses"
    echo "  3. Run as Administrator: $APP_NAME.exe -config config.yaml"
else
    echo "❌ Build failed"
    exit 1
fi
